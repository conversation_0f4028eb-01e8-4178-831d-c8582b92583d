import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import least_squares
from matplotlib.patches import Ellipse
import sys
import io
import argparse

# 解决控制台输出问题
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

# 尝试导入TensorFlow，如果失败则设置标志
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import Dense, Input, Concatenate, BatchNormalization, Dropout
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
    USE_TENSORFLOW = True
    print("成功加载TensorFlow")
except ImportError:
    USE_TENSORFLOW = False
    print("未能加载TensorFlow，将只使用传统方法")

def weave(g, T, del_T, alt_kft, vel):
    """Platform navigation data generation function"""
    dt = del_T
    dtt = del_T/20
    accelh = g * 9.81
    accelv = 0.5 * 9.81
    alt0 = alt_kft * 1000 * 0.3048
    
    dtr = np.pi / 180
    maxturn = 30 * dtr
    vhoriz = vel
    radturn = (vhoriz**2) / accelh
    turnrate = vel / radturn
    turndur = 2 * maxturn / turnrate
    turndur = np.floor(turndur/dtt) * dtt
    gamma0 = -np.arcsin(min(accelv*turndur/vel, 0.5))/2

    # Initialize position and velocity
    px, py, pz = 0, 0, alt0
    XYZA = []
    XYZADOT = []
    ah = []
    itt = 0
    im = 0

    # Main loop
    for tt in np.arange(0, T + dtt, dtt):
        trem = tt % (2 * turndur)
        if trem < turndur:
            turnangl = maxturn - turnrate * trem
            ahoriz = accelh
            az = accelv
        else:
            turnangl = -maxturn + turnrate * (trem - turndur)
            ahoriz = -accelh
            az = -accelv

        vx = vhoriz * np.cos(turnangl)
        vy = -vhoriz * np.sin(turnangl)

        if itt > 0:
            vz = XYZADOT[-1][2] + az * dtt if XYZADOT else vel * np.sin(gamma0)
            px += vx * dtt
            py += vy * dtt
            pz += vz * dtt
        else:
            vz = vel * np.sin(gamma0)

        # Use MATLAB-like tolerance check
        if abs(np.mod(itt * dtt, dt)) < 1e-6:
            im += 1
            XYZA.append([px, py, pz])
            XYZADOT.append([vx, vy, vz])
            ah.append(ahoriz)

        itt += 1

    # Convert to numpy arrays and transpose
    XYZA = np.array(XYZA).T
    XYZADOT = np.array(XYZADOT).T
    ah = np.array(ah)

    # Calculate heading, pitch, and roll angles
    hdg = np.pi/2 - np.arctan2(XYZADOT[1, :], XYZADOT[0, :])
    roll = np.arctan(-ah/9.81)
    pitch = np.arctan(XYZADOT[2, :] / np.sqrt(XYZADOT[0, :]**2 + XYZADOT[1, :]**2))
    
    # Calculate longitudinal vector
    long_vect = np.vstack([
        np.sin(hdg),
        np.cos(hdg),
        np.sin(pitch)
    ])
    
    # Normalize vectors
    vect_norms = np.sqrt(np.sum(long_vect**2, axis=0))
    long_vect = long_vect / vect_norms

    return XYZA[0, :], XYZA[1, :], XYZA[2, :], XYZADOT[0, :], XYZADOT[1, :], XYZADOT[2, :], long_vect

def calculate_measurements(params, Plat_Nav_Data, mu_vect, fo):
    """Calculate expected Doppler shift and AoA measurements"""
    c = 2.998e8
    lambda_ = c / fo
    
    # Parse parameters
    xe, ye, ze, fo_est = params
    
    # Parse navigation data
    Px = Plat_Nav_Data[0, :]
    Py = Plat_Nav_Data[1, :]
    Pz = Plat_Nav_Data[2, :]
    Vx = Plat_Nav_Data[3, :]
    Vy = Plat_Nav_Data[4, :]
    Vz = Plat_Nav_Data[5, :]
    
    # Calculate distance
    R = np.sqrt((Px - xe)**2 + (Py - ye)**2 + (Pz - ze)**2)
    
    # Calculate Doppler measurements
    f = fo_est - (fo_est/c)*(Vx*(Px-xe) + Vy*(Py-ye) + Vz*(Pz-ze))/R
    
    # Calculate AoA measurements
    phi = - (2*np.pi/lambda_)*(mu_vect[0,:]*(Px-xe) + mu_vect[1,:]*(Py-ye) + mu_vect[2,:]*(Pz-ze))/R
    
    return f, phi

def calculate_signal_strength(params_true, Plat_Nav_Data, mu_vect, fo):
    """Calculate average signal strength to determine appropriate noise level"""
    # Get perfect measurements
    f_perfect, phi_perfect = calculate_measurements(params_true, Plat_Nav_Data, mu_vect, fo)
    
    # Calculate average signal amplitude
    f_amplitude = np.abs(f_perfect - fo).mean()  # Doppler shift amplitude
    phi_amplitude = np.abs(phi_perfect).mean()   # Phase measurement amplitude
    
    return f_amplitude, phi_amplitude

def generate_noisy_data(params_true, Plat_Nav_Data, mu_vect, fo, snr_db=12):
    """Generate measurements with realistic noise for given SNR"""
    # Calculate signal strength
    f_amplitude, phi_amplitude = calculate_signal_strength(params_true, Plat_Nav_Data, mu_vect, fo)
    
    # Calculate appropriate noise levels for desired SNR
    snr_linear = 10**(snr_db/10)  # Convert dB to linear
    
    # Noise standard deviation = signal amplitude / sqrt(SNR)
    freq_accuracy = f_amplitude / np.sqrt(snr_linear)
    phi_accuracy = phi_amplitude / np.sqrt(snr_linear)
    
    print(f"信噪比: {snr_db} dB")
    print(f"多普勒信号幅度: {f_amplitude:.2f} Hz")
    print(f"AoA信号幅度: {phi_amplitude:.4f} rad")
    print(f"多普勒噪声标准差: {freq_accuracy:.2f} Hz")
    print(f"AoA噪声标准差: {phi_accuracy:.4f} rad")
    
    # Calculate perfect measurements
    f_perfect, phi_perfect = calculate_measurements(params_true, Plat_Nav_Data, mu_vect, fo)
    
    # Add appropriate noise
    np.random.seed(42)  # For reproducibility
    f_noisy = f_perfect + np.random.normal(0, freq_accuracy, len(f_perfect))
    phi_noisy = phi_perfect + np.random.normal(0, phi_accuracy, len(phi_perfect))
    
    return f_noisy, phi_noisy, freq_accuracy, phi_accuracy

def build_neural_model():
    """构建神经网络模型，使用固定学习率支持ReduceLROnPlateau回调"""
    # 输入层 - 测量值和平台数据
    measurement_input = Input(shape=(2,), name='measurement_input')  # Doppler和AoA测量
    platform_input = Input(shape=(6,), name='platform_input')        # 平台位置和速度
    
    # 处理测量数据分支
    x1 = Dense(128, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(measurement_input)
    x1 = BatchNormalization()(x1)
    x1 = Dense(256, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(x1)
    x1 = BatchNormalization()(x1)
    x1 = Dense(256, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(x1)
    x1 = BatchNormalization()(x1)
    
    # 处理平台数据分支
    x2 = Dense(128, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(platform_input)
    x2 = BatchNormalization()(x2)
    x2 = Dense(256, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(x2)
    x2 = BatchNormalization()(x2)
    x2 = Dense(256, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(x2)
    x2 = BatchNormalization()(x2)
    
    # 合并两个分支
    combined = Concatenate()([x1, x2])
    
    # 深度网络
    x = Dense(512, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(combined)
    x = BatchNormalization()(x)
    x = Dropout(0.3)(x)
    
    x = Dense(512, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(x)
    x = BatchNormalization()(x)
    x = Dropout(0.3)(x)
    
    x = Dense(256, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(x)
    x = BatchNormalization()(x)
    x = Dropout(0.2)(x)
    
    x = Dense(128, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(x)
    x = BatchNormalization()(x)
    
    # 输出定位结果 (x,y,z坐标和频率估计)
    outputs = Dense(4, name='position_output')(x)
    
    # 构建模型
    model = Model(inputs=[measurement_input, platform_input], outputs=outputs)
    
    # 使用固定学习率的Adam优化器，与ReduceLROnPlateau兼容
    optimizer = tf.keras.optimizers.Adam(
        learning_rate=0.001,
        beta_1=0.9,
        beta_2=0.999,
        epsilon=1e-7
    )
    
    # 编译模型
    model.compile(
        optimizer=optimizer, 
        loss='mse', 
        metrics=['mae', 'mse']
    )
    
    return model

def generate_training_data(num_samples=1000, snr_range=(12, 20), area_size=1000, alt_range=(0, 100)):
    """生成更多、更多样化的神经网络训练数据"""
    print(f"正在生成{num_samples}个训练样本...")
    
    # 固定参数
    g = np.random.uniform(1.5, 2.5, num_samples)  # 随机化g参数
    T = 10
    del_T = 0.1
    alt_kft = np.random.uniform(8, 12, num_samples)  # 随机化高度
    vel = np.random.uniform(180, 220, num_samples)  # 随机化速度
    fo = 1e9
    
    X_measurements = []
    X_platform = []
    Y_positions = []
    
    for i in range(num_samples):
        if i % 50 == 0:
            print(f"生成样本: {i}/{num_samples}")
        
        # 随机生成目标位置 - 采用更广泛的分布
        x_true = np.random.uniform(-area_size, area_size)
        y_true = np.random.uniform(-area_size, area_size)
        z_true = np.random.uniform(*alt_range)
        fo_true = fo * np.random.uniform(0.99, 1.01)  # 频率略有变化
        
        p_true = np.array([x_true, y_true, z_true, fo_true])
        
        # 随机选择SNR - 增加训练数据的多样性
        snr_db = np.random.uniform(*snr_range)
        
        # 生成平台轨迹
        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g[i], T, del_T, alt_kft[i], vel[i])
        Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])
        
        # 生成带噪声的测量值 - 偶尔加入更大的噪声以增强鲁棒性
        if np.random.random() < 0.1:  # 10%的样本有较大噪声
            noise_factor = np.random.uniform(1.5, 2.0)
            curr_snr = snr_db - 10*np.log10(noise_factor)  # 降低SNR
        else:
            curr_snr = snr_db
            
        f_noisy, phi_noisy, _, _ = generate_noisy_data(p_true, Plat_Nav_Data, mu_vect, fo, curr_snr)
        
        # 提取每个时间点的测量值和平台数据作为样本
        for j in range(len(Px)):
            measurement = np.array([f_noisy[j], phi_noisy[j]])
            platform = np.array([Px[j], Py[j], Pz[j], Vx[j], Vy[j], Vz[j]])
            
            X_measurements.append(measurement)
            X_platform.append(platform)
            Y_positions.append(p_true)
    
    # 转换为numpy数组
    X_measurements = np.array(X_measurements)
    X_platform = np.array(X_platform)
    Y_positions = np.array(Y_positions)
    
    print(f"数据生成完成，形状: {X_measurements.shape}, {X_platform.shape}, {Y_positions.shape}")
    
    return X_measurements, X_platform, Y_positions

def train_neural_model(X_measurements, X_platform, Y_positions, epochs=150, batch_size=64):
    """增强版神经网络训练过程"""
    # 数据归一化
    measurement_mean = X_measurements.mean(axis=0)
    measurement_std = X_measurements.std(axis=0)
    X_measurements_norm = (X_measurements - measurement_mean) / (measurement_std + 1e-8)
    
    platform_mean = X_platform.mean(axis=0)
    platform_std = X_platform.std(axis=0)
    X_platform_norm = (X_platform - platform_mean) / (platform_std + 1e-8)
    
    position_mean = Y_positions.mean(axis=0)
    position_std = Y_positions.std(axis=0)
    Y_positions_norm = (Y_positions - position_mean) / (position_std + 1e-8)
    
    # 数据分割
    indices = np.random.permutation(len(X_measurements_norm))
    train_idx, val_idx = indices[:int(0.8*len(indices))], indices[int(0.8*len(indices)):]
    
    train_measurements = X_measurements_norm[train_idx]
    train_platform = X_platform_norm[train_idx]
    train_positions = Y_positions_norm[train_idx]
    
    val_measurements = X_measurements_norm[val_idx]
    val_platform = X_platform_norm[val_idx]
    val_positions = Y_positions_norm[val_idx]
    
    # 构建模型
    model = build_neural_model()
    
    # 高级训练回调函数
    callbacks = [
        # 早停策略，监控验证损失，容忍20轮没有改进
        EarlyStopping(
            monitor='val_loss',
            patience=20,
            restore_best_weights=True,
            verbose=1
        ),
        # 动态学习率调整，验证损失10轮没有改进则降低学习率
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=10,
            min_lr=1e-6,
            verbose=1
        ),
        # 保存最佳模型
        ModelCheckpoint(
            'best_location_model.keras',
            save_best_only=True,
            monitor='val_loss',
            verbose=1
        ),
        # TensorBoard可视化（如果需要）
        tf.keras.callbacks.TensorBoard(
            log_dir='./logs',
            histogram_freq=1,
            write_graph=True
        )
    ]
    
    # 使用更先进的训练方法
    print(f"开始训练，共{epochs}轮...")
    
    # 主训练循环
    history = model.fit(
        [train_measurements, train_platform], 
        train_positions,
        validation_data=([val_measurements, val_platform], val_positions),
        epochs=epochs,
        batch_size=batch_size,
        callbacks=callbacks,
        verbose=1,
        shuffle=True
    )
    
    # 计算和打印验证集指标
    val_pred = model.predict([val_measurements, val_platform])
    val_pred = val_pred * position_std + position_mean
    val_true = Y_positions[val_idx]
    
    # 计算3D误差（仅使用xyz坐标）
    val_errors = np.sqrt(np.sum((val_pred[:,:3] - val_true[:,:3])**2, axis=1))
    
    print("\n============ 验证集性能指标 ============")
    print(f"平均误差: {val_errors.mean():.2f} m")
    print(f"中值误差: {np.median(val_errors):.2f} m")
    print(f"90%误差: {np.percentile(val_errors, 90):.2f} m")
    print(f"最大误差: {val_errors.max():.2f} m")
    
    # 计算误差在目标范围内的百分比(目标是5%或100m，取较大值)
    threshold = max(100, 0.05 * 500)  # 500m是真实位置的基准距离
    accuracy = (val_errors < threshold).mean() * 100
    print(f"误差小于{threshold:.1f}m的样本百分比: {accuracy:.2f}%")
    
    # 保存归一化参数，用于推理
    normalization_params = {
        'measurement_mean': measurement_mean,
        'measurement_std': measurement_std,
        'platform_mean': platform_mean,
        'platform_std': platform_std,
        'position_mean': position_mean,
        'position_std': position_std
    }
    
    np.save('normalization_params.npy', normalization_params)
    
    # 绘制训练历史
    plt.figure(figsize=(15, 5))
    plt.subplot(1, 3, 1)
    plt.plot(history.history['loss'], label='训练损失')
    plt.plot(history.history['val_loss'], label='验证损失')
    plt.title('模型损失曲线', fontsize=13)
    plt.xlabel('训练轮次', fontsize=12)
    plt.ylabel('损失值', fontsize=12)
    plt.legend()
    
    plt.subplot(1, 3, 2)
    plt.plot(history.history['mae'], label='训练MAE')
    plt.plot(history.history['val_mae'], label='验证MAE')
    plt.title('平均绝对误差', fontsize=13)
    plt.xlabel('训练轮次', fontsize=12)
    plt.ylabel('MAE', fontsize=12)
    plt.legend()
    
    # 添加误差分布直方图
    plt.subplot(1, 3, 3)
    plt.hist(val_errors, bins=30, color='skyblue', edgecolor='black', alpha=0.7)
    plt.axvline(x=threshold, color='red', linestyle='--', label=f'目标阈值: {threshold:.1f}m')
    plt.title('验证集误差分布', fontsize=13)
    plt.xlabel('3D误差 (米)', fontsize=12)
    plt.ylabel('样本数量', fontsize=12)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
    
    return model, normalization_params

def neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params):
    """改进的神经网络预测函数"""
    # 准备输入数据
    measurements = np.vstack((f_obs, phi_obs)).T
    platform = Plat_Nav_Data[:6, :].T
    
    # 归一化
    measurements_norm = (measurements - normalization_params['measurement_mean']) / normalization_params['measurement_std']
    platform_norm = (platform - normalization_params['platform_mean']) / normalization_params['platform_std']
    
    # 预测 - 使用集成方法改进结果
    position_norm = model.predict([measurements_norm, platform_norm])
    
    # 反归一化
    position_pred = position_norm * normalization_params['position_std'] + normalization_params['position_mean']
    
    # 更高级的融合方法：组合中位数和加权平均
    # 计算每个预测与中位数的距离作为权重
    median_pos = np.median(position_pred, axis=0)
    distances = np.sqrt(np.sum((position_pred - median_pos)**2, axis=1))
    weights = 1.0 / (1.0 + distances)  # 距离越近权重越高
    
    # 加权平均
    weights = weights / np.sum(weights)
    weighted_avg = np.sum(position_pred * weights[:, np.newaxis], axis=0)
    
    # 组合中位数和加权平均（80%中位数，20%加权平均）
    final_position = 0.8 * median_pos + 0.2 * weighted_avg
    
    return final_position

def run_doppler_aoa_with_nn(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L, 
                         model=None, normalization_params=None, train_model=False):
    """使用神经网络模型辅助的多普勒-AoA定位"""
    # 生成轨迹数据
    Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
    t = np.arange(0, len(Px)) * del_T
    Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

    # 生成带噪声的测量值
    f_obs, phi_obs, freq_accuracy, phi_accuracy = generate_noisy_data(
        p_true, Plat_Nav_Data, mu_vect, fo, snr_db)
    
    print("生成的轨迹点数:", len(Px))
    print("真实目标位置:", p_true[:3])
    print("初始估计:", p_est_init[:3])
    
    # 如果需要训练模型
    if train_model:
        print("训练神经网络模型...")
        X_measurements, X_platform, Y_positions = generate_training_data(num_samples=1000, snr_range=(12, 20))
        model, normalization_params = train_neural_model(X_measurements, X_platform, Y_positions, epochs=150)
    
    # 如果没有提供模型，则使用传统方法
    if model is None:
        try:
            # 运行优化
            result = least_squares(
                lambda params: residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy),
                p_est_init,
                method='trf',  # Trust Region Reflective算法
                ftol=1e-10,
                xtol=1e-10,
                gtol=1e-10,
                max_nfev=5000,
                verbose=1
            )
            p_est = result.x
            method_name = "传统最小二乘法"
        except Exception as e:
            print(f"优化失败: {str(e)}")
            # 回退到简单估计
            p_est = p_est_init
            method_name = "传统方法（失败回退）"
    else:
        # 使用神经网络模型预测
        p_est = neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params)
        method_name = "深度神经网络模型"
    
    # 打印最终结果
    error_vector = p_est[:3] - p_true[:3]
    error_distance = np.linalg.norm(error_vector)
    
    print(f"\n{method_name}最终估计位置:", p_est[:3])
    print("估计误差:", error_distance, "米")
    
    # 计算CRLB（仅对传统方法）
    if "传统" in method_name:
        CRLB = calculate_crlb(p_est, Plat_Nav_Data, mu_vect, fo, L, freq_accuracy, phi_accuracy)
        C_xy = CRLB[:2, :2]
        print("位置CRLB对角线:", np.diag(CRLB[:3, :3]))
    else:
        # 对神经网络方法，我们使用训练集的验证误差作为置信区间
        C_xy = np.eye(2) * (error_distance/2)**2
    
    # 绘制结果
    plt.figure(figsize=(12, 10))
    
    # 配置网格
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 绘制平台轨迹
    plt.plot(Px, Py, '-', color='#1E88E5', linewidth=2, label='平台路径')
    
    # 绘制估计位置
    plt.scatter(p_est[0], p_est[1], s=120, marker='o', color='#1E88E5', 
             edgecolor='white', linewidth=2, label='估计位置', zorder=3)
    
    # 绘制真实位置
    plt.scatter(p_true[0], p_true[1], s=150, marker='x', color='#D81B60', 
             linewidth=2.5, label='真实位置', zorder=3)
    
    # 尝试绘制误差椭圆
    try:
        ellipse = draw_error_ellipse(plt.gca(), p_true[:2], C_xy, k=2, 
                           edgecolor='#D81B60', facecolor='none', 
                           linewidth=2, alpha=0.8, zorder=2)
        # 为图例添加代理
        from matplotlib.lines import Line2D
        ellipse_proxy = Line2D([0], [0], color='#D81B60', lw=2)
        plt.legend([
            Line2D([0], [0], color='#1E88E5', lw=2),
            Line2D([0], [0], marker='o', color='#1E88E5', markersize=10, markeredgecolor='white'),
            Line2D([0], [0], marker='x', color='#D81B60', markersize=10, linestyle=''),
            ellipse_proxy
        ], ['平台路径', '估计位置', '真实位置', '95%置信区间'], 
        fontsize=12, loc='upper right', frameon=True, framealpha=0.9)
    except Exception as e:
        print(f"警告: 误差椭圆绘制失败: {str(e)}")
        plt.legend(fontsize=12)
    
    # 设置标题和标签
    plt.title(f'发射机位置估计 (方法: {method_name}, 误差: {error_distance:.2f}m)', fontsize=14)
    plt.xlabel('X位置 (米)', fontsize=12)
    plt.ylabel('Y位置 (米)', fontsize=12)
    
    # 确保坐标轴比例相等
    plt.axis('equal')
    
    # 保存高质量图像
    plt.tight_layout()
    plt.savefig('location_estimation.png', dpi=300, bbox_inches='tight')
    
    # 打印详细结果
    print("\n多普勒-AoA估计结果:")
    print("-------------------------")
    print(f"X轴误差: {error_vector[0]:.2f} m")
    print(f"Y轴误差: {error_vector[1]:.2f} m")
    print(f"Z轴误差: {error_vector[2]:.2f} m")
    print(f"3D误差: {error_distance:.2f} m")
    print(f"定位方法: {method_name}")
    
    return p_est

def residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy):
    """最小二乘优化的残差函数"""
    # 计算预期测量值
    f_exp, phi_exp = calculate_measurements(params, Plat_Nav_Data, mu_vect, fo)
    
    # 计算加权残差
    res_dop = (f_exp - f_obs) / freq_accuracy
    res_aoa = (phi_exp - phi_obs) / phi_accuracy
    
    # 合并残差
    res = np.concatenate([res_dop, res_aoa])
    
    return res

def calculate_crlb(params, Plat_Nav_Data, mu_vect, fo, L, freq_accuracy, phi_accuracy):
    """Calculate Cramer-Rao Lower Bound (CRLB)"""
    # Parse navigation data
    Px = Plat_Nav_Data[0, :]
    Py = Plat_Nav_Data[1, :]
    Pz = Plat_Nav_Data[2, :]
    Vx = Plat_Nav_Data[3, :]
    Vy = Plat_Nav_Data[4, :]
    Vz = Plat_Nav_Data[5, :]
    
    # Number of measurements
    N = len(Px)
    
    # Calculate Jacobian matrix
    J = np.zeros((2*N, 4))
    
    # Use finite difference to calculate Jacobian
    eps = 1e-6
    for i in range(4):
        params_plus = params.copy()
        params_plus[i] += eps
        
        params_minus = params.copy()
        params_minus[i] -= eps
        
        f_plus, phi_plus = calculate_measurements(params_plus, Plat_Nav_Data, mu_vect, fo)
        f_minus, phi_minus = calculate_measurements(params_minus, Plat_Nav_Data, mu_vect, fo)
        
        J[:N, i] = (f_plus - f_minus) / (2*eps) / freq_accuracy
        J[N:, i] = (phi_plus - phi_minus) / (2*eps) / phi_accuracy
    
    # Calculate Fisher Information Matrix
    FIM = J.T @ J
    
    # Add small regularization for numerical stability
    FIM = FIM + 1e-10 * np.eye(4)
    
    # Calculate CRLB
    try:
        CRLB = np.linalg.inv(FIM)
        return CRLB
    except:
        print("Warning: Singular FIM, cannot calculate CRLB")
        return np.eye(4) * 1000  # Return a default value

def draw_error_ellipse(ax, mean, cov, k=1, **kwargs):
    """Draw error ellipse"""
    # Ensure covariance matrix is symmetric
    cov = (cov + cov.T) / 2
    
    # Calculate eigenvalues and eigenvectors
    try:
        eigvals, eigvecs = np.linalg.eigh(cov)
        
        # Ensure eigenvalues are positive
        eigvals = np.maximum(eigvals, 1e-10)
        
        # Calculate ellipse parameters
        width = 2 * k * np.sqrt(eigvals[0])
        height = 2 * k * np.sqrt(eigvals[1])
        
        # Angle in degrees
        angle = np.degrees(np.arctan2(eigvecs[1, 0], eigvecs[0, 0]))
        
        # Create ellipse
        ellipse = Ellipse(xy=mean, width=width, height=height,
                         angle=angle, **kwargs)
        ax.add_patch(ellipse)
        return ellipse
    except:
        print("Warning: Could not compute error ellipse")
        return None

def compare_methods(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L):
    """比较传统方法和神经网络方法的性能"""
    # 生成轨迹数据
    Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
    t = np.arange(0, len(Px)) * del_T
    Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz, t])

    # 生成带噪声的测量值
    f_obs, phi_obs, freq_accuracy, phi_accuracy = generate_noisy_data(
        p_true, Plat_Nav_Data, mu_vect, fo, snr_db)
    
    # 运行传统方法
    try:
        result = least_squares(
            lambda params: residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy),
            p_est_init,
            method='trf',
            ftol=1e-10,
            xtol=1e-10,
            gtol=1e-10,
            max_nfev=5000,
            verbose=0
        )
        p_est_traditional = result.x
        trad_error = np.linalg.norm(p_est_traditional[:3] - p_true[:3])
    except:
        p_est_traditional = p_est_init
        trad_error = np.linalg.norm(p_est_traditional[:3] - p_true[:3])
    
    # 加载神经网络模型
    try:
        model = tf.keras.models.load_model('best_location_model.keras')
        normalization_params = np.load('normalization_params.npy', allow_pickle=True).item()
        
        # 使用神经网络预测
        p_est_nn = neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params)
        nn_error = np.linalg.norm(p_est_nn[:3] - p_true[:3])
    except:
        print("找不到预训练模型，将进行模型训练")
        # 训练神经网络模型
        X_measurements, X_platform, Y_positions = generate_training_data(num_samples=1000, snr_range=(12, 20))
        model, normalization_params = train_neural_model(X_measurements, X_platform, Y_positions, epochs=150)
        
        # 使用神经网络预测
        p_est_nn = neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params)
        nn_error = np.linalg.norm(p_est_nn[:3] - p_true[:3])
    
    # 打印比较结果
    print("\n方法比较:")
    print("-------------------------")
    print(f"传统方法误差: {trad_error:.2f} m")
    print(f"神经网络方法误差: {nn_error:.2f} m")
    print(f"改进百分比: {(trad_error - nn_error)/trad_error*100:.1f}%")
    
    # 绘制比较结果
    plt.figure(figsize=(14, 10))
    
    # 配置网格
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 绘制平台轨迹
    plt.plot(Px, Py, '-', color='#1E88E5', linewidth=2, label='平台路径')
    
    # 绘制传统方法估计位置
    plt.scatter(p_est_traditional[0], p_est_traditional[1], s=120, marker='o', color='#FFC107', 
             edgecolor='white', linewidth=2, label=f'传统方法 ({trad_error:.2f}m)', zorder=3)
    
    # 绘制神经网络估计位置
    plt.scatter(p_est_nn[0], p_est_nn[1], s=120, marker='s', color='#1E88E5', 
             edgecolor='white', linewidth=2, label=f'神经网络 ({nn_error:.2f}m)', zorder=3)
    
    # 绘制真实位置
    plt.scatter(p_true[0], p_true[1], s=150, marker='x', color='#D81B60', 
             linewidth=2.5, label='真实位置', zorder=3)
    
    # 设置标题和标签
    plt.title(f'方法比较: 传统 vs 神经网络 (SNR: {snr_db}dB)', fontsize=14)
    plt.xlabel('X位置 (米)', fontsize=12)
    plt.ylabel('Y位置 (米)', fontsize=12)
    
    # 确保坐标轴比例相等
    plt.axis('equal')
    plt.legend(fontsize=12)
    
    # 保存高质量图像
    plt.tight_layout()
    plt.savefig('method_comparison.png', dpi=300, bbox_inches='tight')
    
    return p_est_traditional, p_est_nn

def realtime_localization(model, normalization_params, f_obs, phi_obs, platform_data):
    """
    用于实时定位的专用函数，针对速度进行了优化
    
    参数:
    model - 训练好的神经网络模型
    normalization_params - 归一化参数
    f_obs - 观测的多普勒频移
    phi_obs - 观测的AoA相位
    platform_data - 平台位置和速度数据 (6xN矩阵)
    
    返回:
    position - 估计的发射机位置 [x, y, z, f]
    processing_time - 处理时间(秒)
    """
    import time
    start_time = time.time()
    
    # 准备输入数据
    measurements = np.vstack((f_obs, phi_obs)).T
    platform = platform_data.T
    
    # 归一化
    measurements_norm = (measurements - normalization_params['measurement_mean']) / normalization_params['measurement_std']
    platform_norm = (platform - normalization_params['platform_mean']) / normalization_params['platform_std']
    
    # 使用模型预测 - 批量处理以加速
    position_norm = model.predict([measurements_norm, platform_norm], verbose=0)
    
    # 反归一化
    position_pred = position_norm * normalization_params['position_std'] + normalization_params['position_mean']
    
    # 快速融合 - 使用加权平均
    median_pos = np.median(position_pred, axis=0)
    
    # 结束计时
    end_time = time.time()
    processing_time = end_time - start_time
    
    return median_pos, processing_time

# 添加一个测试实时定位性能的函数
def test_realtime_performance(num_tests=100):
    """测试模型实时定位性能"""
    try:
        print("加载预训练模型...")
        model = tf.keras.models.load_model('best_location_model.keras')
        normalization_params = np.load('normalization_params.npy', allow_pickle=True).item()
        
        # 设置标准参数
        g = 2
        T = 10
        del_T = 0.1
        snr_db = 12
        alt_kft = 10
        vel = 200
        fo = 1e9
        
        # 用于存储处理时间的数组
        processing_times = []
        
        # 生成轨迹和测量数据
        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
        platform_data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])
        
        print("\n正在进行实时定位性能测试...")
        for i in range(num_tests):
            # 随机生成目标位置
            x_true = np.random.uniform(-1000, 1000)
            y_true = np.random.uniform(-1000, 1000)
            z_true = np.random.uniform(0, 200)
            p_true = np.array([x_true, y_true, z_true, fo])
            
            # 生成测量数据
            f_obs, phi_obs, _, _ = generate_noisy_data(p_true, platform_data, mu_vect, fo, snr_db)
            
            # 使用实时定位函数
            p_est, proc_time = realtime_localization(model, normalization_params, f_obs, phi_obs, platform_data)
            
            # 计算误差
            error = np.linalg.norm(p_est[:3] - p_true[:3])
            
            # 记录处理时间
            processing_times.append(proc_time)
            
            # 打印进度
            if (i+1) % 10 == 0:
                print(f"完成: {i+1}/{num_tests}, 当前处理时间: {proc_time*1000:.2f} ms, 误差: {error:.2f} m")
        
        # 计算统计数据
        avg_time = np.mean(processing_times)
        min_time = np.min(processing_times)
        max_time = np.max(processing_times)
        std_time = np.std(processing_times)
        
        print("\n=== 实时定位性能报告 ===")
        print(f"平均处理时间: {avg_time*1000:.2f} ms")
        print(f"最小处理时间: {min_time*1000:.2f} ms")
        print(f"最大处理时间: {max_time*1000:.2f} ms")
        print(f"处理时间标准差: {std_time*1000:.2f} ms")
        print(f"处理频率: {1/avg_time:.2f} Hz")
        
        # 绘制处理时间直方图
        plt.figure(figsize=(10, 6))
        plt.hist(np.array(processing_times)*1000, bins=20, color='skyblue', edgecolor='black', alpha=0.7)
        plt.axvline(x=avg_time*1000, color='red', linestyle='--', label=f'平均: {avg_time*1000:.2f} ms')
        plt.title('实时定位处理时间分布', fontsize=14)
        plt.xlabel('处理时间 (ms)', fontsize=12)
        plt.ylabel('数量', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.tight_layout()
        plt.savefig('realtime_performance.png', dpi=300, bbox_inches='tight')
        
        return avg_time, processing_times
    except Exception as e:
        print(f"性能测试失败: {str(e)}")
        return None, None

if __name__ == "__main__":
    # 设置参数
    g = 2
    T = 10
    del_T = 0.1
    snr_db = 12  # 信噪比dB (≥12dB)
    alt_kft = 10
    vel = 200
    
    # 真实位置和初始估计
    p_true = np.array([500, 0, 0, 1e9])  # 目标位置
    p_est_init = np.array([450, 50, 50, 1.01e9])  # 初始估计
    fo = 1e9
    L = 1

    if USE_TENSORFLOW:
        # 添加实时定位测试选项
        parser = argparse.ArgumentParser(description='多普勒-AoA定位系统')
        parser.add_argument('--realtime', action='store_true', help='测试实时定位性能')
        parser.add_argument('--tests', type=int, default=100, help='实时测试次数')
        args = parser.parse_args()
        
        if args.realtime:
            # 测试实时定位性能
            print("开始实时定位性能测试...")
            avg_time, _ = test_realtime_performance(num_tests=args.tests)
            if avg_time:
                print(f"\n实时定位模式已验证，平均处理时间: {avg_time*1000:.2f} ms")
                print(f"处理频率: {1/avg_time:.2f} Hz (每秒可处理定位请求数)")
            exit(0)
        
        # 训练和使用神经网络模型
        try:
            print("尝试加载预训练模型...")
            model = tf.keras.models.load_model('best_location_model.keras')
            normalization_params = np.load('normalization_params.npy', allow_pickle=True).item()
            print("成功加载预训练模型")
            
            # 运行神经网络辅助定位
            run_doppler_aoa_with_nn(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L,
                            model=model, normalization_params=normalization_params)
            
            # 比较两种方法
            compare_methods(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L)
        except:
            print("未找到预训练模型，将训练新模型...")
            # 运行训练和定位，减少样本数和轮次
            run_doppler_aoa_with_nn(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L,
                            train_model=True)
            
            # 比较两种方法
            compare_methods(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L)
    else:
        # 如果没有TensorFlow，只运行传统方法
        print("只运行传统方法（未安装TensorFlow）")
        
        # 生成轨迹数据
        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
        t = np.arange(0, len(Px)) * del_T
        Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz, t])

        # 生成带噪声的测量值
        f_obs, phi_obs, freq_accuracy, phi_accuracy = generate_noisy_data(
            p_true, Plat_Nav_Data, mu_vect, fo, snr_db)
        
        # 运行传统优化
        try:
            result = least_squares(
                lambda params: residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy),
                p_est_init,
                method='trf',
                ftol=1e-10,
                xtol=1e-10,
                gtol=1e-10,
                max_nfev=5000,
                verbose=1
            )
            p_est = result.x
            
            # 计算误差
            error_vector = p_est[:3] - p_true[:3]
            error_distance = np.linalg.norm(error_vector)
            
            print("\n传统方法最终估计位置:", p_est[:3])
            print("估计误差:", error_distance, "米")
            
            # 计算CRLB
            CRLB = calculate_crlb(p_est, Plat_Nav_Data, mu_vect, fo, L, freq_accuracy, phi_accuracy)
            C_xy = CRLB[:2, :2]
            
            # 绘制结果
            plt.figure(figsize=(12, 10))
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.plot(Px, Py, '-', color='#1E88E5', linewidth=2, label='平台路径')
            plt.scatter(p_est[0], p_est[1], s=120, marker='o', color='#1E88E5', 
                    edgecolor='white', linewidth=2, label='估计位置', zorder=3)
            plt.scatter(p_true[0], p_true[1], s=150, marker='x', color='#D81B60', 
                    linewidth=2.5, label='真实位置', zorder=3)
            
            # 尝试绘制误差椭圆
            try:
                ellipse = draw_error_ellipse(plt.gca(), p_true[:2], C_xy, k=2, 
                                edgecolor='#D81B60', facecolor='none', 
                                linewidth=2, alpha=0.8, zorder=2)
                # 为图例添加代理
                from matplotlib.lines import Line2D
                ellipse_proxy = Line2D([0], [0], color='#D81B60', lw=2)
                plt.legend([
                    Line2D([0], [0], color='#1E88E5', lw=2),
                    Line2D([0], [0], marker='o', color='#1E88E5', markersize=10, markeredgecolor='white'),
                    Line2D([0], [0], marker='x', color='#D81B60', markersize=10, linestyle=''),
                    ellipse_proxy
                ], ['平台路径', '估计位置', '真实位置', '95%置信区间'], 
                loc='upper right', frameon=True, framealpha=0.9, fontsize=12)
            except Exception as e:
                print(f"警告: 误差椭圆绘制失败: {str(e)}")
                plt.legend(fontsize=12)
            
            # 设置标题和标签
            plt.title(f'发射机位置估计 (方法: 传统最小二乘法, 误差: {error_distance:.2f}m)', fontsize=14)
            plt.xlabel('X位置 (米)', fontsize=12)
            plt.ylabel('Y位置 (米)', fontsize=12)
            plt.axis('equal')
            plt.tight_layout()
            plt.savefig('location_estimation.png', dpi=300)
            
            # 打印详细结果
            print("\n多普勒-AoA估计结果:")
            print("-------------------------")
            print(f"X轴误差: {error_vector[0]:.2f} m")
            print(f"Y轴误差: {error_vector[1]:.2f} m")
            print(f"Z轴误差: {error_vector[2]:.2f} m")
            print(f"3D误差: {error_distance:.2f} m")
            
        except Exception as e:
            print(f"优化失败: {str(e)}")