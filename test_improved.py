#!/usr/bin/env python3
"""
测试改进的神经网络定位方法
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import tensorflow as tf
    from tensorflow.keras.models import Model
    from tensorflow.keras.layers import Dense, Input, BatchNormalization, Dropout, Add, Multiply
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
    USE_TENSORFLOW = True
    print("TensorFlow 可用")
except ImportError:
    USE_TENSORFLOW = False
    print("TensorFlow 不可用")

# 导入基础函数
from aoa_dopplor import (
    weave, calculate_measurements, calculate_signal_strength,
    generate_noisy_data
)

def create_enhanced_features(measurements, platform_data):
    """创建增强的物理特征"""
    f_obs, phi_obs = measurements[:, 0], measurements[:, 1]
    px, py, pz, vx, vy, vz = platform_data[:, 0], platform_data[:, 1], platform_data[:, 2], platform_data[:, 3], platform_data[:, 4], platform_data[:, 5]
    
    # 确保所有特征都是相同长度的数组
    n_samples = len(f_obs)
    
    # 1. 原始测量值
    features_list = [f_obs, phi_obs]
    
    # 2. 平台状态
    features_list.extend([px, py, pz, vx, vy, vz])
    
    # 3. 物理相关特征
    platform_speed = np.sqrt(vx**2 + vy**2 + vz**2)
    platform_distance = np.sqrt(px**2 + py**2 + pz**2)
    features_list.extend([platform_speed, platform_distance])
    
    # 4. 多普勒相关特征
    f_normalized = (f_obs - 1e9) / 1e6  # 归一化频率偏移
    features_list.append(f_normalized)
    
    # 5. 角度相关特征
    phi_sin = np.sin(phi_obs)
    phi_cos = np.cos(phi_obs)
    features_list.extend([phi_sin, phi_cos])
    
    # 6. 时间序列特征（如果有多个时间点）
    if n_samples > 1:
        f_diff = np.gradient(f_obs)
        phi_diff = np.gradient(phi_obs)
        features_list.extend([f_diff, phi_diff])
    else:
        features_list.extend([np.zeros(n_samples), np.zeros(n_samples)])
    
    # 转换为numpy数组并转置
    features_array = np.array(features_list).T
    
    return features_array

def build_advanced_model():
    """构建高级神经网络模型"""
    # 输入层 - 使用增强特征
    feature_input = Input(shape=(15,), name='feature_input')  # 增强特征维度
    
    # 特征预处理层
    x = Dense(128, activation='relu')(feature_input)
    x = BatchNormalization()(x)
    x = Dropout(0.1)(x)
    
    # 残差块1
    residual1 = x
    x = Dense(256, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.2)(x)
    x = Dense(128, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Add()([x, residual1])  # 残差连接
    
    # 残差块2
    residual2 = x
    x = Dense(256, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.2)(x)
    x = Dense(128, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Add()([x, residual2])  # 残差连接
    
    # 注意力机制
    attention_weights = Dense(128, activation='softmax')(x)
    x = Multiply()([x, attention_weights])
    
    # 最终处理层
    x = Dense(64, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.1)(x)
    
    # 输出层
    position_output = Dense(4, name='position_output')(x)
    
    model = Model(inputs=feature_input, outputs=position_output)
    
    # 使用自适应学习率
    optimizer = Adam(learning_rate=0.001, beta_1=0.9, beta_2=0.999)
    
    # 改进的损失函数
    def adaptive_loss(y_true, y_pred):
        # 位置损失 - 使用自适应权重
        position_error = y_true[:, :3] - y_pred[:, :3]
        position_distance = tf.sqrt(tf.reduce_sum(tf.square(position_error), axis=1))
        
        # 对于距离较远的目标，使用相对误差
        true_distance = tf.sqrt(tf.reduce_sum(tf.square(y_true[:, :3]), axis=1))
        relative_error = position_distance / (true_distance + 1e-6)
        
        # 自适应权重：近距离目标要求绝对精度，远距离目标要求相对精度
        weight_absolute = tf.exp(-true_distance / 500.0)  # 近距离权重
        weight_relative = 1.0 - weight_absolute  # 远距离权重
        
        loss_absolute = tf.reduce_mean(weight_absolute * position_distance)
        loss_relative = tf.reduce_mean(weight_relative * relative_error * 100.0)
        
        # 频率损失
        freq_loss = tf.reduce_mean(tf.square(y_true[:, 3] - y_pred[:, 3])) * 1e-12
        
        return loss_absolute + loss_relative + freq_loss
    
    model.compile(optimizer=optimizer, loss=adaptive_loss, metrics=['mae'])
    
    return model

def generate_high_quality_training_data(num_samples=1000):
    """生成高质量训练数据"""
    print(f"生成高质量训练数据，样本数量: {num_samples}")
    print("确保所有样本SNR ≥ 12dB，增强数据多样性")
    
    X_measurements = []
    X_platform = []
    Y_positions = []
    
    # 生成样本
    for i in range(num_samples):
        # 随机化平台参数以增加数据多样性
        g = np.random.uniform(1.5, 2.5)  # 随机化g参数
        T = np.random.uniform(8, 12)     # 随机化观测时间
        del_T = 0.1
        alt_kft = np.random.uniform(8, 12)  # 随机化高度
        vel = np.random.uniform(180, 220)   # 随机化速度
        fo = 1e9
        
        # 更均匀的目标位置分布
        if i < num_samples * 0.2:  # 20% 近距离目标
            x_true = np.random.uniform(-300, 300)
            y_true = np.random.uniform(-300, 300)
            z_true = np.random.uniform(0, 150)
        elif i < num_samples * 0.4:  # 20% 中近距离目标
            x_true = np.random.uniform(-600, 600)
            y_true = np.random.uniform(-600, 600)
            z_true = np.random.uniform(0, 300)
        elif i < num_samples * 0.6:  # 20% 中距离目标
            x_true = np.random.uniform(-900, 900)
            y_true = np.random.uniform(-900, 900)
            z_true = np.random.uniform(0, 450)
        elif i < num_samples * 0.8:  # 20% 远距离目标
            x_true = np.random.uniform(-1200, 1200)
            y_true = np.random.uniform(-1200, 1200)
            z_true = np.random.uniform(0, 600)
        else:  # 20% 极远距离目标
            x_true = np.random.uniform(-1500, 1500)
            y_true = np.random.uniform(-1500, 1500)
            z_true = np.random.uniform(0, 800)
            
        p_true = np.array([x_true, y_true, z_true, fo])
        
        # 生成平台轨迹
        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
        platform_data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])
        
        # 更均匀的SNR分布，确保≥12dB
        if i < num_samples * 0.25:  # 25% 高SNR
            snr_db = np.random.uniform(18, 22)
        elif i < num_samples * 0.5:  # 25% 中高SNR
            snr_db = np.random.uniform(15, 18)
        elif i < num_samples * 0.75:  # 25% 中SNR
            snr_db = np.random.uniform(13, 15)
        else:  # 25% 最低SNR（但仍≥12dB）
            snr_db = np.random.uniform(12, 13)
            
        # 生成简化的高斯噪声数据
        f_obs, phi_obs, _, _ = generate_noisy_data(p_true, platform_data, mu_vect, fo, snr_db)
        
        # 存储每个时间点的数据作为独立样本
        for j in range(len(f_obs)):
            measurement = np.array([f_obs[j], phi_obs[j]])
            platform_state = np.array([Px[j], Py[j], Pz[j], Vx[j], Vy[j], Vz[j]])
            
            X_measurements.append(measurement)
            X_platform.append(platform_state)
            Y_positions.append(p_true)
        
        # 显示进度
        if (i + 1) % (num_samples // 10) == 0:
            print(f"已生成 {i+1}/{num_samples} 样本，当前SNR: {snr_db:.1f}dB")
    
    # 转换为numpy数组
    X_measurements = np.array(X_measurements)
    X_platform = np.array(X_platform)
    Y_positions = np.array(Y_positions)
    
    print(f"训练数据生成完成: {len(X_measurements)} 个测量点")
    print(f"数据形状: 测量值{X_measurements.shape}, 平台{X_platform.shape}, 位置{Y_positions.shape}")
    
    return X_measurements, X_platform, Y_positions

if __name__ == "__main__":
    if USE_TENSORFLOW:
        print("开始测试改进的神经网络方法...")
        
        # 生成测试数据
        X_measurements, X_platform, Y_positions = generate_high_quality_training_data(num_samples=100)
        
        # 测试特征工程
        print("\n测试特征工程...")
        features = create_enhanced_features(X_measurements, X_platform)
        print(f"增强特征形状: {features.shape}")
        
        # 测试模型构建
        print("\n测试模型构建...")
        model = build_advanced_model()
        print("模型构建成功")
        print(model.summary())
        
        print("\n改进的神经网络方法测试完成！")
    else:
        print("TensorFlow不可用，无法测试神经网络方法")
