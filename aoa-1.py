import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import least_squares
from matplotlib.patches import Ellipse
import sys
import io
import argparse
import os

# 解决控制台输出问题
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

# 尝试导入TensorFlow，如果失败则设置标志
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import Dense, Input, Concatenate, BatchNormalization, Dropout, Add
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
    USE_TENSORFLOW = True
    print("成功加载TensorFlow")
except ImportError:
    USE_TENSORFLOW = False
    print("未能加载TensorFlow，将只使用传统方法")

def weave(g, T, del_T, alt_kft, vel):
    """Platform navigation data generation function"""
    dt = del_T
    dtt = del_T/20
    accelh = g * 9.81
    accelv = 0.5 * 9.81
    alt0 = alt_kft * 1000 * 0.3048

    dtr = np.pi / 180
    maxturn = 30 * dtr
    vhoriz = vel
    radturn = (vhoriz**2) / accelh
    turnrate = vel / radturn
    turndur = 2 * maxturn / turnrate
    turndur = np.floor(turndur/dtt) * dtt
    gamma0 = -np.arcsin(min(accelv*turndur/vel, 0.5))/2

    # Initialize position and velocity
    px, py, pz = 0, 0, alt0
    XYZA = []
    XYZADOT = []
    ah = []
    itt = 0
    im = 0

    # Main loop
    for tt in np.arange(0, T + dtt, dtt):
        trem = tt % (2 * turndur)
        if trem < turndur:
            turnangl = maxturn - turnrate * trem
            ahoriz = accelh
            az = accelv
        else:
            turnangl = -maxturn + turnrate * (trem - turndur)
            ahoriz = -accelh
            az = -accelv

        vx = vhoriz * np.cos(turnangl)
        vy = -vhoriz * np.sin(turnangl)

        if itt > 0:
            vz = XYZADOT[-1][2] + az * dtt if XYZADOT else vel * np.sin(gamma0)
            px += vx * dtt
            py += vy * dtt
            pz += vz * dtt
        else:
            vz = vel * np.sin(gamma0)

        # Use MATLAB-like tolerance check
        if abs(np.mod(itt * dtt, dt)) < 1e-6:
            im += 1
            XYZA.append([px, py, pz])
            XYZADOT.append([vx, vy, vz])
            ah.append(ahoriz)

        itt += 1

    # Convert to numpy arrays and transpose
    XYZA = np.array(XYZA).T
    XYZADOT = np.array(XYZADOT).T
    ah = np.array(ah)

    # Calculate heading, pitch, and roll angles
    hdg = np.pi/2 - np.arctan2(XYZADOT[1, :], XYZADOT[0, :])
    roll = np.arctan(-ah/9.81)
    pitch = np.arctan(XYZADOT[2, :] / np.sqrt(XYZADOT[0, :]**2 + XYZADOT[1, :]**2))

    # Calculate longitudinal vector
    long_vect = np.vstack([
        np.sin(hdg),
        np.cos(hdg),
        np.sin(pitch)
    ])

    # Normalize vectors
    vect_norms = np.sqrt(np.sum(long_vect**2, axis=0))
    long_vect = long_vect / vect_norms

    return XYZA[0, :], XYZA[1, :], XYZA[2, :], XYZADOT[0, :], XYZADOT[1, :], XYZADOT[2, :], long_vect

def calculate_measurements(params, Plat_Nav_Data, mu_vect, fo):
    """Calculate expected Doppler shift and AoA measurements"""
    c = 2.998e8
    lambda_ = c / fo

    # Parse parameters
    xe, ye, ze, fo_est = params

    # Parse navigation data
    Px = Plat_Nav_Data[0, :]
    Py = Plat_Nav_Data[1, :]
    Pz = Plat_Nav_Data[2, :]
    Vx = Plat_Nav_Data[3, :]
    Vy = Plat_Nav_Data[4, :]
    Vz = Plat_Nav_Data[5, :]

    # Calculate distance
    R = np.sqrt((Px - xe)**2 + (Py - ye)**2 + (Pz - ze)**2)

    # Calculate Doppler measurements
    f = fo_est - (fo_est/c)*(Vx*(Px-xe) + Vy*(Py-ye) + Vz*(Pz-ze))/R

    # Calculate AoA measurements
    phi = - (2*np.pi/lambda_)*(mu_vect[0,:]*(Px-xe) + mu_vect[1,:]*(Py-ye) + mu_vect[2,:]*(Pz-ze))/R

    return f, phi

def calculate_signal_strength(params_true, Plat_Nav_Data, mu_vect, fo):
    """Calculate average signal strength to determine appropriate noise level"""
    # Get perfect measurements
    f_perfect, phi_perfect = calculate_measurements(params_true, Plat_Nav_Data, mu_vect, fo)

    # Calculate average signal amplitude
    f_amplitude = np.abs(f_perfect - fo).mean()  # Doppler shift amplitude
    phi_amplitude = np.abs(phi_perfect).mean()   # Phase measurement amplitude

    return f_amplitude, phi_amplitude

def generate_noisy_data(params_true, Plat_Nav_Data, mu_vect, fo, snr_db=12):
    """Generate measurements with simple Gaussian noise for given SNR (≥12dB)"""
    # 确保SNR不低于12dB
    if snr_db < 12:
        print(f"警告: SNR {snr_db}dB 低于要求的12dB，调整为12dB")
        snr_db = 12

    # Calculate signal strength
    f_amplitude, phi_amplitude = calculate_signal_strength(params_true, Plat_Nav_Data, mu_vect, fo)

    # Calculate appropriate noise levels for desired SNR
    snr_linear = 10**(snr_db/10)  # Convert dB to linear

    # Noise standard deviation = signal amplitude / sqrt(SNR)
    freq_accuracy = f_amplitude / np.sqrt(snr_linear)
    phi_accuracy = phi_amplitude / np.sqrt(snr_linear)

    # Calculate perfect measurements
    f_perfect, phi_perfect = calculate_measurements(params_true, Plat_Nav_Data, mu_vect, fo)

    # Add simple Gaussian noise only
    f_noisy = f_perfect + np.random.normal(0, freq_accuracy, len(f_perfect))
    phi_noisy = phi_perfect + np.random.normal(0, phi_accuracy, len(phi_perfect))

    # 验证实际SNR
    signal_power_f = np.mean(np.abs(f_perfect - fo)**2)
    noise_power_f = np.mean((f_noisy - f_perfect)**2)
    actual_snr_f = 10 * np.log10(signal_power_f / noise_power_f) if noise_power_f > 0 else float('inf')

    signal_power_phi = np.mean(np.abs(phi_perfect)**2)
    noise_power_phi = np.mean((phi_noisy - phi_perfect)**2)
    actual_snr_phi = 10 * np.log10(signal_power_phi / noise_power_phi) if noise_power_phi > 0 else float('inf')

    # 只在详细模式下打印信息
    if snr_db >= 12:  # 只在主要测试时打印
        print(f"目标SNR: {snr_db:.1f} dB")
        print(f"实际SNR - 多普勒: {actual_snr_f:.1f} dB, AoA: {actual_snr_phi:.1f} dB")
        print(f"多普勒信号幅度: {f_amplitude:.2f} Hz, 噪声标准差: {freq_accuracy:.2f} Hz")
        print(f"AoA信号幅度: {phi_amplitude:.4f} rad, 噪声标准差: {phi_accuracy:.4f} rad")

    return f_noisy, phi_noisy, freq_accuracy, phi_accuracy

def generate_high_quality_training_data(num_samples=10000, quick_test=False):
    """生成高质量训练数据，确保SNR≥12dB，覆盖更广泛的场景"""
    # 根据模式选择样本数量
    actual_samples = 1000 if quick_test else num_samples

    print(f"生成高质量训练数据，样本数量: {actual_samples}")
    print("确保所有样本SNR ≥ 12dB，增强数据多样性")

    X_measurements = []
    X_platform = []
    Y_positions = []

    # 生成样本
    for i in range(actual_samples):
        # 随机化平台参数以增加数据多样性
        g = np.random.uniform(1.5, 2.5)  # 随机化g参数
        T = np.random.uniform(8, 12)     # 随机化观测时间
        del_T = 0.1
        alt_kft = np.random.uniform(8, 12)  # 随机化高度
        vel = np.random.uniform(180, 220)   # 随机化速度
        fo = 1e9

        # 更均匀的目标位置分布
        if i < actual_samples * 0.2:  # 20% 近距离目标
            x_true = np.random.uniform(-300, 300)
            y_true = np.random.uniform(-300, 300)
            z_true = np.random.uniform(0, 150)
        elif i < actual_samples * 0.4:  # 20% 中近距离目标
            x_true = np.random.uniform(-600, 600)
            y_true = np.random.uniform(-600, 600)
            z_true = np.random.uniform(0, 300)
        elif i < actual_samples * 0.6:  # 20% 中距离目标
            x_true = np.random.uniform(-900, 900)
            y_true = np.random.uniform(-900, 900)
            z_true = np.random.uniform(0, 450)
        elif i < actual_samples * 0.8:  # 20% 远距离目标
            x_true = np.random.uniform(-1200, 1200)
            y_true = np.random.uniform(-1200, 1200)
            z_true = np.random.uniform(0, 600)
        else:  # 20% 极远距离目标
            x_true = np.random.uniform(-1500, 1500)
            y_true = np.random.uniform(-1500, 1500)
            z_true = np.random.uniform(0, 800)

        p_true = np.array([x_true, y_true, z_true, fo])

        # 生成平台轨迹
        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
        platform_data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

        # 更均匀的SNR分布，确保≥12dB
        if i < actual_samples * 0.25:  # 25% 高SNR
            snr_db = np.random.uniform(18, 22)
        elif i < actual_samples * 0.5:  # 25% 中高SNR
            snr_db = np.random.uniform(15, 18)
        elif i < actual_samples * 0.75:  # 25% 中SNR
            snr_db = np.random.uniform(13, 15)
        else:  # 25% 最低SNR（但仍≥12dB）
            snr_db = np.random.uniform(12, 13)

        # 生成简化的高斯噪声数据
        f_obs, phi_obs, _, _ = generate_noisy_data(p_true, platform_data, mu_vect, fo, snr_db)

        # 存储每个时间点的数据作为独立样本
        for j in range(len(f_obs)):
            measurement = np.array([f_obs[j], phi_obs[j]])
            platform_state = np.array([Px[j], Py[j], Pz[j], Vx[j], Vy[j], Vz[j]])

            X_measurements.append(measurement)
            X_platform.append(platform_state)
            Y_positions.append(p_true)

        # 显示进度
        if (i + 1) % (actual_samples // 10) == 0:
            print(f"已生成 {i+1}/{actual_samples} 样本，当前SNR: {snr_db:.1f}dB")

    # 转换为numpy数组
    X_measurements = np.array(X_measurements)
    X_platform = np.array(X_platform)
    Y_positions = np.array(Y_positions)

    print(f"训练数据生成完成: {len(X_measurements)} 个测量点")
    print(f"数据形状: 测量值{X_measurements.shape}, 平台{X_platform.shape}, 位置{Y_positions.shape}")

    return X_measurements, X_platform, Y_positions

def build_improved_model():
    """改进的神经网络模型，专注于定位精度"""
    measurement_input = Input(shape=(2,), name='measurement_input')
    platform_input = Input(shape=(6,), name='platform_input')

    # 测量数据处理分支 - 简化架构
    x1 = Dense(64, activation='relu')(measurement_input)
    x1 = BatchNormalization()(x1)
    x1 = Dense(128, activation='relu')(x1)
    x1 = BatchNormalization()(x1)

    # 平台数据处理分支 - 简化架构
    x2 = Dense(64, activation='relu')(platform_input)
    x2 = BatchNormalization()(x2)
    x2 = Dense(128, activation='relu')(x2)
    x2 = BatchNormalization()(x2)

    # 合并分支
    combined = Concatenate()([x1, x2])

    # 主处理层 - 减少复杂度防止过拟合
    x = Dense(256, activation='relu')(combined)
    x = BatchNormalization()(x)
    x = Dropout(0.3)(x)

    x = Dense(128, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.2)(x)

    x = Dense(64, activation='relu')(x)
    x = BatchNormalization()(x)

    # 输出层
    position_output = Dense(4, name='position_output')(x)

    model = Model(inputs=[measurement_input, platform_input], outputs=position_output)

    # 使用更高的学习率和更简单的优化器
    optimizer = Adam(learning_rate=0.001)

    # 使用Huber损失，对异常值更鲁棒
    def huber_loss(y_true, y_pred, delta=1.0):
        error = y_true - y_pred
        abs_error = tf.abs(error)
        quadratic = tf.minimum(abs_error, delta)
        linear = abs_error - quadratic
        return 0.5 * tf.square(quadratic) + delta * linear

    # 专注于位置精度的损失函数
    def position_focused_loss(y_true, y_pred):
        # 位置损失（前3个维度）- 使用Huber损失
        position_loss = tf.reduce_mean(huber_loss(y_true[:, :3], y_pred[:, :3], delta=100.0))
        # 频率损失（第4个维度）- 权重较小
        freq_loss = tf.reduce_mean(tf.square(y_true[:, 3] - y_pred[:, 3]))
        return position_loss + 0.01 * freq_loss

    model.compile(optimizer=optimizer, loss=position_focused_loss, metrics=['mae'])

    return model

def train_with_validation(X_measurements, X_platform, Y_positions, epochs=200, quick_test=False):
    """改进的训练和验证策略"""
    # 数据归一化 - 使用更稳定的归一化方法
    measurement_mean = np.median(X_measurements, axis=0)
    measurement_std = np.std(X_measurements, axis=0) + 1e-8
    X_measurements_norm = (X_measurements - measurement_mean) / measurement_std

    platform_mean = np.median(X_platform, axis=0)
    platform_std = np.std(X_platform, axis=0) + 1e-8
    X_platform_norm = (X_platform - platform_mean) / platform_std

    position_mean = np.median(Y_positions, axis=0)
    position_std = np.std(Y_positions, axis=0) + 1e-8
    Y_positions_norm = (Y_positions - position_mean) / position_std

    # 创建normalization_params字典
    normalization_params = {
        'measurement_mean': measurement_mean,
        'measurement_std': measurement_std,
        'platform_mean': platform_mean,
        'platform_std': platform_std,
        'position_mean': position_mean,
        'position_std': position_std
    }
    
    # 保存归一化参数
    np.save('normalization_params.npy', normalization_params)

    # 分割数据集 - 使用分层抽样
    indices = np.random.permutation(len(X_measurements_norm))
    train_idx = indices[:int(0.7*len(indices))]
    val_idx = indices[int(0.7*len(indices)):int(0.85*len(indices))]
    test_idx = indices[int(0.85*len(indices)):]
    
    train_measurements = X_measurements_norm[train_idx]
    train_platform = X_platform_norm[train_idx]
    train_positions = Y_positions_norm[train_idx]

    val_measurements = X_measurements_norm[val_idx]
    val_platform = X_platform_norm[val_idx]
    val_positions = Y_positions_norm[val_idx]

    # 根据模式设置训练参数
    actual_epochs = 20 if quick_test else epochs
    batch_size = 32 if quick_test else 64
    model_count = 1 if quick_test else 3
    
    # 改进的早停和学习率策略
    callbacks = [
        EarlyStopping(
            monitor='val_loss',
            patience=15 if quick_test else 30,  # 增加耐心
            restore_best_weights=True,
            mode='min'
        ),
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.7,  # 更温和的学习率降低
            patience=10 if quick_test else 20,  # 增加耐心
            min_lr=1e-6,
            mode='min'
        ),
        ModelCheckpoint(
            'best_location_model.keras',
            monitor='val_loss',
            save_best_only=True,
            mode='min'
        )
    ]
    
    # 多次训练并选择最佳模型
    best_val_error = float('inf')
    best_model = None
    
    for i in range(model_count):
        print(f"\n训练模型 {i+1}/{model_count}...")
        model = build_improved_model()
        
        history = model.fit(
            [train_measurements, train_platform],
            train_positions,
            validation_data=([val_measurements, val_platform], val_positions),
            epochs=actual_epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        # 评估验证集性能
        val_pred = model.predict([val_measurements, val_platform])
        val_pred = val_pred * position_std + position_mean
        val_true = Y_positions[val_idx]
        
        val_errors = np.sqrt(np.sum((val_pred[:,:3] - val_true[:,:3])**2, axis=1))
        avg_error = np.median(val_errors)
        
        if avg_error < best_val_error:
            best_val_error = avg_error
            best_model = model
            print(f"找到更好的模型，验证误差: {avg_error:.2f} m")
    
    return best_model, normalization_params

def neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params):
    """改进的神经网络预测函数"""
    # 准备输入数据
    measurements = np.vstack((f_obs, phi_obs)).T
    platform = Plat_Nav_Data[:6, :].T

    # 归一化
    measurements_norm = (measurements - normalization_params['measurement_mean']) / normalization_params['measurement_std']
    platform_norm = (platform - normalization_params['platform_mean']) / normalization_params['platform_std']

    # 预测 - 使用集成方法改进结果
    position_norm = model.predict([measurements_norm, platform_norm])

    # 反归一化
    position_pred = position_norm * normalization_params['position_std'] + normalization_params['position_mean']

    # 更高级的融合方法：组合中位数和加权平均
    # 计算每个预测与中位数的距离作为权重
    median_pos = np.median(position_pred, axis=0)
    distances = np.sqrt(np.sum((position_pred - median_pos)**2, axis=1))
    weights = 1.0 / (1.0 + distances)  # 距离越近权重越高

    # 加权平均
    weights = weights / np.sum(weights)
    weighted_avg = np.sum(position_pred * weights[:, np.newaxis], axis=0)

    # 组合中位数和加权平均（80%中位数，20%加权平均）
    final_position = 0.8 * median_pos + 0.2 * weighted_avg

    return final_position

def run_doppler_aoa_with_nn(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L,
                         model=None, normalization_params=None, train_model=False, quick_test=False):
    """使用神经网络模型辅助的多普勒-AoA定位，支持快速验证模式"""
    # 生成轨迹数据
    Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
    t = np.arange(0, len(Px)) * del_T
    Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

    # 生成带噪声的测量值
    f_obs, phi_obs, freq_accuracy, phi_accuracy = generate_noisy_data(
        p_true, Plat_Nav_Data, mu_vect, fo, snr_db)

    print("生成的轨迹点数:", len(Px))
    print("真实目标位置:", p_true[:3])
    print("初始估计:", p_est_init[:3])

    # 运行传统方法
    print("\n运行传统定位方法...")
    try:
        result = least_squares(
            lambda params: residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy),
            p_est_init,
            method='trf',
            ftol=1e-10,
            xtol=1e-10,
            gtol=1e-10,
            max_nfev=5000,
            verbose=1
        )
        p_est_traditional = result.x
        trad_error = np.linalg.norm(p_est_traditional[:3] - p_true[:3])
        print(f"\n传统最小二乘法最终估计位置:", p_est_traditional[:3])
        print("传统方法估计误差:", trad_error, "米")
    except Exception as e:
        print(f"传统方法优化失败: {str(e)}")
        p_est_traditional = p_est_init
        trad_error = np.linalg.norm(p_est_traditional[:3] - p_true[:3])
        print(f"\n传统方法（失败回退）最终估计位置:", p_est_traditional[:3])
        print("传统方法估计误差:", trad_error, "米")

    # 如果需要训练模型
    if train_model:
        print("\n训练神经网络模型...")
        X_measurements, X_platform, Y_positions = generate_high_quality_training_data(num_samples=10000, quick_test=quick_test)
        model, normalization_params = train_with_validation(X_measurements, X_platform, Y_positions,
                                                          epochs=100, quick_test=quick_test)

    # 使用神经网络模型预测
    if model is not None:
        print("\n运行神经网络定位方法...")
        p_est = neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params)
        method_name = "深度神经网络模型"
    else:
        p_est = p_est_traditional
        method_name = "传统方法"

    # 打印最终结果
    error_vector = p_est[:3] - p_true[:3]
    error_distance = np.linalg.norm(error_vector)

    print(f"\n{method_name}最终估计位置:", p_est[:3])
    print("估计误差:", error_distance, "米")

    # 验证精度要求
    meets_requirement, max_allowed_error = validate_accuracy_requirements(p_true, p_est, error_distance)

    # 计算CRLB（仅对传统方法）
    if "传统" in method_name:
        CRLB = calculate_crlb(p_est, Plat_Nav_Data, mu_vect, fo, L, freq_accuracy, phi_accuracy)
        print("位置CRLB对角线:", np.diag(CRLB[:3, :3]))
    else:
        # 对神经网络方法，我们使用训练集的验证误差作为置信区间
        pass

    # 绘制结果
    plt.figure(figsize=(12, 10))

    # 配置网格
    plt.grid(True, linestyle='--', alpha=0.7)

    # 绘制平台轨迹
    plt.plot(Px, Py, '-', color='#1E88E5', linewidth=2, label='平台路径')

    # 绘制传统方法估计位置
    plt.scatter(p_est_traditional[0], p_est_traditional[1], s=120, marker='o', color='#FFC107',
             edgecolor='white', linewidth=2, label=f'传统方法 ({trad_error:.2f}m)', zorder=3)

    # 绘制神经网络估计位置
    plt.scatter(p_est[0], p_est[1], s=120, marker='s', color='#1E88E5',
             edgecolor='white', linewidth=2, label=f'神经网络 ({error_distance:.2f}m)', zorder=3)

    # 绘制真实位置
    plt.scatter(p_true[0], p_true[1], s=150, marker='x', color='#D81B60',
             linewidth=2.5, label='真实位置', zorder=3)

    # 设置标题和标签
    plt.title(f'多普勒-AoA定位方法比较 (SNR: {snr_db}dB)', fontsize=14)
    plt.xlabel('X位置 (米)', fontsize=12)
    plt.ylabel('Y位置 (米)', fontsize=12)

    # 确保坐标轴比例相等
    plt.axis('equal')
    plt.legend(fontsize=12)

    # 保存高质量图像
    plt.tight_layout()
    plt.savefig('location_estimation.png', dpi=300, bbox_inches='tight')

    # 打印详细结果
    print("\n多普勒-AoA估计结果:")
    print("-------------------------")
    print(f"X轴误差: {error_vector[0]:.2f} m")
    print(f"Y轴误差: {error_vector[1]:.2f} m")
    print(f"Z轴误差: {error_vector[2]:.2f} m")
    print(f"3D误差: {error_distance:.2f} m")
    print(f"定位方法: {method_name}")
    print(f"相对传统方法改进: {(trad_error - error_distance)/trad_error*100:.1f}%")

    return p_est

def residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy):
    """最小二乘优化的残差函数"""
    # 计算预期测量值
    f_exp, phi_exp = calculate_measurements(params, Plat_Nav_Data, mu_vect, fo)

    # 计算加权残差
    res_dop = (f_exp - f_obs) / freq_accuracy
    res_aoa = (phi_exp - phi_obs) / phi_accuracy

    # 合并残差
    res = np.concatenate([res_dop, res_aoa])

    return res

def calculate_crlb(params, Plat_Nav_Data, mu_vect, fo, L, freq_accuracy, phi_accuracy):
    """Calculate Cramer-Rao Lower Bound (CRLB)"""
    # Parse navigation data
    Px = Plat_Nav_Data[0, :]
    Py = Plat_Nav_Data[1, :]
    Pz = Plat_Nav_Data[2, :]
    Vx = Plat_Nav_Data[3, :]
    Vy = Plat_Nav_Data[4, :]
    Vz = Plat_Nav_Data[5, :]

    # Number of measurements
    N = len(Px)

    # Calculate Jacobian matrix
    J = np.zeros((2*N, 4))

    # Use finite difference to calculate Jacobian
    eps = 1e-6
    for i in range(4):
        params_plus = params.copy()
        params_plus[i] += eps

        params_minus = params.copy()
        params_minus[i] -= eps

        f_plus, phi_plus = calculate_measurements(params_plus, Plat_Nav_Data, mu_vect, fo)
        f_minus, phi_minus = calculate_measurements(params_minus, Plat_Nav_Data, mu_vect, fo)

        J[:N, i] = (f_plus - f_minus) / (2*eps) / freq_accuracy
        J[N:, i] = (phi_plus - phi_minus) / (2*eps) / phi_accuracy

    # Calculate Fisher Information Matrix
    FIM = J.T @ J

    # Add small regularization for numerical stability
    FIM = FIM + 1e-10 * np.eye(4)

    # Calculate CRLB
    try:
        CRLB = np.linalg.inv(FIM)
        return CRLB
    except:
        print("Warning: Singular FIM, cannot calculate CRLB")
        return np.eye(4) * 1000  # Return a default value

def draw_error_ellipse(ax, mean, cov, k=1, **kwargs):
    """Draw error ellipse"""
    # Ensure covariance matrix is symmetric
    cov = (cov + cov.T) / 2

    # Calculate eigenvalues and eigenvectors
    try:
        eigvals, eigvecs = np.linalg.eigh(cov)

        # Ensure eigenvalues are positive
        eigvals = np.maximum(eigvals, 1e-10)

        # Calculate ellipse parameters
        width = 2 * k * np.sqrt(eigvals[0])
        height = 2 * k * np.sqrt(eigvals[1])

        # Angle in degrees
        angle = np.degrees(np.arctan2(eigvecs[1, 0], eigvecs[0, 0]))

        # Create ellipse
        ellipse = Ellipse(xy=mean, width=width, height=height,
                         angle=angle, **kwargs)
        ax.add_patch(ellipse)
        return ellipse
    except:
        print("Warning: Could not compute error ellipse")
        return None

def compare_methods(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L):
    """比较传统方法和神经网络方法的性能"""
    # 生成轨迹数据
    Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
    t = np.arange(0, len(Px)) * del_T
    Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

    # 生成带噪声的测量值
    f_obs, phi_obs, freq_accuracy, phi_accuracy = generate_noisy_data(
        p_true, Plat_Nav_Data, mu_vect, fo, snr_db)

    # 运行传统方法
    try:
        result = least_squares(
            lambda params: residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy),
            p_est_init,
            method='trf',
            ftol=1e-10,
            xtol=1e-10,
            gtol=1e-10,
            max_nfev=5000,
            verbose=0
        )
        p_est_traditional = result.x
        trad_error = np.linalg.norm(p_est_traditional[:3] - p_true[:3])
    except:
        p_est_traditional = p_est_init
        trad_error = np.linalg.norm(p_est_traditional[:3] - p_true[:3])

    # 加载神经网络模型
    try:
        model = tf.keras.models.load_model('best_location_model.keras')
        normalization_params = np.load('normalization_params.npy', allow_pickle=True).item()

        # 使用神经网络预测
        p_est_nn = neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params)
        nn_error = np.linalg.norm(p_est_nn[:3] - p_true[:3])
    except:
        print("找不到预训练模型，将进行模型训练")
        # 训练神经网络模型
        X_measurements, X_platform, Y_positions = generate_high_quality_training_data(num_samples=10000)
        model, normalization_params = train_with_validation(X_measurements, X_platform, Y_positions, epochs=200)

        # 使用神经网络预测
        p_est_nn = neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params)
        nn_error = np.linalg.norm(p_est_nn[:3] - p_true[:3])

    # 打印比较结果
    print("\n方法比较:")
    print("-------------------------")
    print(f"传统方法误差: {trad_error:.2f} m")
    print(f"神经网络方法误差: {nn_error:.2f} m")
    print(f"改进百分比: {(trad_error - nn_error)/trad_error*100:.1f}%")

    # 绘制比较结果
    plt.figure(figsize=(14, 10))

    # 配置网格
    plt.grid(True, linestyle='--', alpha=0.7)

    # 绘制平台轨迹
    plt.plot(Px, Py, '-', color='#1E88E5', linewidth=2, label='平台路径')

    # 绘制传统方法估计位置
    plt.scatter(p_est_traditional[0], p_est_traditional[1], s=120, marker='o', color='#FFC107',
             edgecolor='white', linewidth=2, label=f'传统方法 ({trad_error:.2f}m)', zorder=3)

    # 绘制神经网络估计位置
    plt.scatter(p_est_nn[0], p_est_nn[1], s=120, marker='s', color='#1E88E5',
             edgecolor='white', linewidth=2, label=f'神经网络 ({nn_error:.2f}m)', zorder=3)

    # 绘制真实位置
    plt.scatter(p_true[0], p_true[1], s=150, marker='x', color='#D81B60',
             linewidth=2.5, label='真实位置', zorder=3)

    # 设置标题和标签
    plt.title(f'方法比较: 传统 vs 神经网络 (SNR: {snr_db}dB)', fontsize=14)
    plt.xlabel('X位置 (米)', fontsize=12)
    plt.ylabel('Y位置 (米)', fontsize=12)

    # 确保坐标轴比例相等
    plt.axis('equal')
    plt.legend(fontsize=12)

    # 保存高质量图像
    plt.tight_layout()
    plt.savefig('method_comparison.png', dpi=300, bbox_inches='tight')

    return p_est_traditional, p_est_nn

def realtime_localization(model, normalization_params, f_obs, phi_obs, platform_data):
    """
    用于实时定位的专用函数，针对速度进行了优化

    参数:
    model - 训练好的神经网络模型
    normalization_params - 归一化参数
    f_obs - 观测的多普勒频移
    phi_obs - 观测的AoA相位
    platform_data - 平台位置和速度数据 (6xN矩阵)

    返回:
    position - 估计的发射机位置 [x, y, z, f]
    processing_time - 处理时间(秒)
    """
    import time
    start_time = time.time()

    # 准备输入数据
    measurements = np.vstack((f_obs, phi_obs)).T
    platform = platform_data.T

    # 归一化
    measurements_norm = (measurements - normalization_params['measurement_mean']) / normalization_params['measurement_std']
    platform_norm = (platform - normalization_params['platform_mean']) / normalization_params['platform_std']

    # 使用模型预测 - 批量处理以加速
    position_norm = model.predict([measurements_norm, platform_norm], verbose=0)

    # 反归一化
    position_pred = position_norm * normalization_params['position_std'] + normalization_params['position_mean']

    # 快速融合 - 使用加权平均
    median_pos = np.median(position_pred, axis=0)

    # 结束计时
    end_time = time.time()
    processing_time = end_time - start_time

    return median_pos, processing_time

# 添加一个测试实时定位性能的函数
def validate_accuracy_requirements(p_true, p_est, error_distance):
    """验证定位精度是否满足要求：误差不超过真实距离的5%或100m"""
    true_distance = np.linalg.norm(p_true[:3])
    max_allowed_error = max(true_distance * 0.05, 100.0)  # 5%或100m，取较大值

    meets_requirement = error_distance <= max_allowed_error

    print(f"\n=== 精度要求验证 ===")
    print(f"真实距离: {true_distance:.2f} m")
    print(f"最大允许误差: {max_allowed_error:.2f} m (真实距离5%或100m)")
    print(f"实际误差: {error_distance:.2f} m")
    print(f"是否满足要求: {'✓ 是' if meets_requirement else '✗ 否'}")

    if meets_requirement:
        print(f"误差占真实距离比例: {error_distance/true_distance*100:.2f}%")
    else:
        print(f"超出允许误差: {error_distance - max_allowed_error:.2f} m")

    return meets_requirement, max_allowed_error

def test_realtime_performance(num_tests=100):
    """测试模型实时定位性能"""
    try:
        print("加载预训练模型...")
        model = tf.keras.models.load_model('best_location_model.keras')
        normalization_params = np.load('normalization_params.npy', allow_pickle=True).item()

        # 设置标准参数
        g = 2
        T = 10
        del_T = 0.1
        snr_db = 12
        alt_kft = 10
        vel = 200
        fo = 1e9

        # 用于存储处理时间的数组
        processing_times = []

        # 生成轨迹和测量数据
        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
        platform_data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

        print("\n正在进行实时定位性能测试...")
        for i in range(num_tests):
            # 随机生成目标位置
            x_true = np.random.uniform(-1000, 1000)
            y_true = np.random.uniform(-1000, 1000)
            z_true = np.random.uniform(0, 200)
            p_true = np.array([x_true, y_true, z_true, fo])

            # 生成测量数据
            f_obs, phi_obs, _, _ = generate_noisy_data(p_true, platform_data, mu_vect, fo, snr_db)

            # 使用实时定位函数
            p_est, proc_time = realtime_localization(model, normalization_params, f_obs, phi_obs, platform_data)

            # 计算误差
            error = np.linalg.norm(p_est[:3] - p_true[:3])

            # 记录处理时间
            processing_times.append(proc_time)

            # 打印进度
            if (i+1) % 10 == 0:
                print(f"完成: {i+1}/{num_tests}, 当前处理时间: {proc_time*1000:.2f} ms, 误差: {error:.2f} m")

        # 计算统计数据
        avg_time = np.mean(processing_times)
        min_time = np.min(processing_times)
        max_time = np.max(processing_times)
        std_time = np.std(processing_times)

        print("\n=== 实时定位性能报告 ===")
        print(f"平均处理时间: {avg_time*1000:.2f} ms")
        print(f"最小处理时间: {min_time*1000:.2f} ms")
        print(f"最大处理时间: {max_time*1000:.2f} ms")
        print(f"处理时间标准差: {std_time*1000:.2f} ms")
        print(f"处理频率: {1/avg_time:.2f} Hz")

        # 绘制处理时间直方图
        plt.figure(figsize=(10, 6))
        plt.hist(np.array(processing_times)*1000, bins=20, color='skyblue', edgecolor='black', alpha=0.7)
        plt.axvline(x=avg_time*1000, color='red', linestyle='--', label=f'平均: {avg_time*1000:.2f} ms')
        plt.title('实时定位处理时间分布', fontsize=14)
        plt.xlabel('处理时间 (ms)', fontsize=12)
        plt.ylabel('数量', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.tight_layout()
        plt.savefig('realtime_performance.png', dpi=300, bbox_inches='tight')

        return avg_time, processing_times
    except Exception as e:
        print(f"性能测试失败: {str(e)}")
        return None, None

if __name__ == "__main__":
    # 设置基本参数
    g = 2
    T = 10
    del_T = 0.1
    snr_db = 12  # 信噪比dB (≥12dB)
    alt_kft = 10
    vel = 200
    fo = 1e9
    L = 1

    # 随机生成测试场景
    np.random.seed()  # 使用当前时间作为随机种子
    x_true = np.random.uniform(-1000, 1000)  # 随机X坐标
    y_true = np.random.uniform(-1000, 1000)  # 随机Y坐标
    z_true = np.random.uniform(0, 500)       # 随机Z坐标
    p_true = np.array([x_true, y_true, z_true, fo])  # 真实目标位置

    # 随机生成初始估计（在真实位置附近）
    x_init = x_true + np.random.uniform(-100, 100)
    y_init = y_true + np.random.uniform(-100, 100)
    z_init = z_true + np.random.uniform(-50, 50)
    p_est_init = np.array([x_init, y_init, z_init, fo * (1 + np.random.uniform(-0.01, 0.01))])

    print("测试场景参数:")
    print(f"真实目标位置: [{x_true:.2f}, {y_true:.2f}, {z_true:.2f}]")
    print(f"初始估计位置: [{x_init:.2f}, {y_init:.2f}, {z_init:.2f}]")
    print(f"要求: SNR ≥ 12dB, 误差 ≤ 真实距离5% 或 100m")

    if USE_TENSORFLOW:
        # 添加命令行参数
        parser = argparse.ArgumentParser(description='多普勒-AoA定位系统')
        parser.add_argument('--realtime', action='store_true', help='测试实时定位性能')
        parser.add_argument('--tests', type=int, default=100, help='实时测试次数')
        parser.add_argument('--quick', action='store_true', help='使用快速验证模式')
        args = parser.parse_args()

        if args.realtime:
            # 测试实时定位性能
            print("开始实时定位性能测试...")
            avg_time, _ = test_realtime_performance(num_tests=args.tests)
            if avg_time:
                print(f"\n实时定位模式已验证，平均处理时间: {avg_time*1000:.2f} ms")
                print(f"处理频率: {1/avg_time:.2f} Hz (每秒可处理定位请求数)")
            exit(0)
            
        try:
            # 检查是否存在预训练模型
            if os.path.exists('best_location_model.keras') and os.path.exists('normalization_params.npy'):
                print("找到预训练模型，直接使用...")
                model = tf.keras.models.load_model('best_location_model.keras')
                normalization_params = np.load('normalization_params.npy', allow_pickle=True).item()
                run_doppler_aoa_with_nn(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L, 
                                      model=model, normalization_params=normalization_params)
            else:
                # 训练新模型并运行定位
                print("未找到预训练模型，将训练新神经网络模型...")
                if args.quick:
                    print("使用快速验证模式...")
                    run_doppler_aoa_with_nn(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L, 
                                          train_model=True, quick_test=True)
                else:
                    print("使用完整训练模式...")
                    run_doppler_aoa_with_nn(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L, 
                                          train_model=True, quick_test=False)
        except Exception as e:
            print(f"神经网络模型运行失败: {str(e)}")
            print("回退到传统方法...")
            # 运行传统方法
            Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
            Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])
            f_obs, phi_obs, freq_accuracy, phi_accuracy = generate_noisy_data(p_true, Plat_Nav_Data, mu_vect, fo, snr_db)
            
            result = least_squares(
                lambda params: residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy),
                p_est_init,
                method='trf',
                ftol=1e-10,
                xtol=1e-10,
                gtol=1e-10,
                max_nfev=5000,
                verbose=1
            )
            print("\n传统方法最终估计位置:", result.x[:3])
            print("估计误差:", np.linalg.norm(result.x[:3] - p_true[:3]), "米")
    else:
        # 如果没有TensorFlow，只运行传统方法
        print("只运行传统方法（未安装TensorFlow）")
        # 其余代码保持不变...