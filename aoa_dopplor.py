import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import least_squares
from matplotlib.patches import Ellipse
import sys
import io
import argparse

# 解决控制台输出问题
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

# 尝试导入TensorFlow，如果失败则设置标志
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import Dense, Input, Concatenate, BatchNormalization, Dropout, GaussianNoise
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
    USE_TENSORFLOW = True
    print("成功加载TensorFlow")
except ImportError:
    USE_TENSORFLOW = False
    print("未能加载TensorFlow，将只使用传统方法")

def weave(g, T, del_T, alt_kft, vel):
    """Platform navigation data generation function"""
    dt = del_T
    dtt = del_T/20
    accelh = g * 9.81
    accelv = 0.5 * 9.81
    alt0 = alt_kft * 1000 * 0.3048

    dtr = np.pi / 180
    maxturn = 30 * dtr
    vhoriz = vel
    radturn = (vhoriz**2) / accelh
    turnrate = vel / radturn
    turndur = 2 * maxturn / turnrate
    turndur = np.floor(turndur/dtt) * dtt
    gamma0 = -np.arcsin(min(accelv*turndur/vel, 0.5))/2

    # Initialize position and velocity
    px, py, pz = 0, 0, alt0
    XYZA = []
    XYZADOT = []
    ah = []
    itt = 0
    im = 0

    # Main loop
    for tt in np.arange(0, T + dtt, dtt):
        trem = tt % (2 * turndur)
        if trem < turndur:
            turnangl = maxturn - turnrate * trem
            ahoriz = accelh
            az = accelv
        else:
            turnangl = -maxturn + turnrate * (trem - turndur)
            ahoriz = -accelh
            az = -accelv

        vx = vhoriz * np.cos(turnangl)
        vy = -vhoriz * np.sin(turnangl)

        if itt > 0:
            vz = XYZADOT[-1][2] + az * dtt if XYZADOT else vel * np.sin(gamma0)
            px += vx * dtt
            py += vy * dtt
            pz += vz * dtt
        else:
            vz = vel * np.sin(gamma0)

        # Use MATLAB-like tolerance check
        if abs(np.mod(itt * dtt, dt)) < 1e-6:
            im += 1
            XYZA.append([px, py, pz])
            XYZADOT.append([vx, vy, vz])
            ah.append(ahoriz)

        itt += 1

    # Convert to numpy arrays and transpose
    XYZA = np.array(XYZA).T
    XYZADOT = np.array(XYZADOT).T
    ah = np.array(ah)

    # Calculate heading, pitch, and roll angles
    hdg = np.pi/2 - np.arctan2(XYZADOT[1, :], XYZADOT[0, :])
    roll = np.arctan(-ah/9.81)
    pitch = np.arctan(XYZADOT[2, :] / np.sqrt(XYZADOT[0, :]**2 + XYZADOT[1, :]**2))

    # Calculate longitudinal vector
    long_vect = np.vstack([
        np.sin(hdg),
        np.cos(hdg),
        np.sin(pitch)
    ])

    # Normalize vectors
    vect_norms = np.sqrt(np.sum(long_vect**2, axis=0))
    long_vect = long_vect / vect_norms

    return XYZA[0, :], XYZA[1, :], XYZA[2, :], XYZADOT[0, :], XYZADOT[1, :], XYZADOT[2, :], long_vect

def calculate_measurements(params, Plat_Nav_Data, mu_vect, fo):
    """Calculate expected Doppler shift and AoA measurements"""
    c = 2.998e8
    lambda_ = c / fo

    # Parse parameters
    xe, ye, ze, fo_est = params

    # Parse navigation data
    Px = Plat_Nav_Data[0, :]
    Py = Plat_Nav_Data[1, :]
    Pz = Plat_Nav_Data[2, :]
    Vx = Plat_Nav_Data[3, :]
    Vy = Plat_Nav_Data[4, :]
    Vz = Plat_Nav_Data[5, :]

    # Calculate distance
    R = np.sqrt((Px - xe)**2 + (Py - ye)**2 + (Pz - ze)**2)

    # Calculate Doppler measurements
    f = fo_est - (fo_est/c)*(Vx*(Px-xe) + Vy*(Py-ye) + Vz*(Pz-ze))/R

    # Calculate AoA measurements
    phi = - (2*np.pi/lambda_)*(mu_vect[0,:]*(Px-xe) + mu_vect[1,:]*(Py-ye) + mu_vect[2,:]*(Pz-ze))/R

    return f, phi

def calculate_signal_strength(params_true, Plat_Nav_Data, mu_vect, fo):
    """Calculate average signal strength to determine appropriate noise level"""
    # Get perfect measurements
    f_perfect, phi_perfect = calculate_measurements(params_true, Plat_Nav_Data, mu_vect, fo)

    # Calculate average signal amplitude
    f_amplitude = np.abs(f_perfect - fo).mean()  # Doppler shift amplitude
    phi_amplitude = np.abs(phi_perfect).mean()   # Phase measurement amplitude

    return f_amplitude, phi_amplitude

def generate_noisy_data(params_true, Plat_Nav_Data, mu_vect, fo, snr_db=12, complex_noise=True, verbose=True):
    """Generate measurements with simplified but realistic noise for given SNR

    Args:
        params_true: True parameters [x, y, z, fo]
        Plat_Nav_Data: Platform navigation data
        mu_vect: Direction vectors
        fo: Carrier frequency
        snr_db: Signal-to-noise ratio in dB (enforced to be ≥12dB)
        complex_noise: Whether to generate complex noise with realistic characteristics
        verbose: Whether to print detailed information

    Returns:
        f_noisy: Noisy Doppler measurements
        phi_noisy: Noisy AoA measurements
        freq_accuracy: Frequency measurement accuracy
        phi_accuracy: AoA measurement accuracy
    """
    # Enforce SNR condition (not less than 12dB)
    snr_db = max(12, snr_db)

    # Calculate signal strength
    f_amplitude, phi_amplitude = calculate_signal_strength(params_true, Plat_Nav_Data, mu_vect, fo)

    # Calculate appropriate noise levels for desired SNR
    snr_linear = 10**(snr_db/10)  # Convert dB to linear

    # Noise standard deviation = signal amplitude / sqrt(SNR)
    freq_accuracy = f_amplitude / np.sqrt(snr_linear)
    phi_accuracy = phi_amplitude / np.sqrt(snr_linear)

    # 只在详细模式下打印信息
    if verbose:
        print(f"信噪比: {snr_db} dB")
        print(f"多普勒信号幅度: {f_amplitude:.2f} Hz")
        print(f"AoA信号幅度: {phi_amplitude:.4f} rad")
        print(f"多普勒噪声标准差: {freq_accuracy:.2f} Hz")
        print(f"AoA噪声标准差: {phi_accuracy:.4f} rad")

    # Calculate perfect measurements
    f_perfect, phi_perfect = calculate_measurements(params_true, Plat_Nav_Data, mu_vect, fo)

    # 不再设置固定的随机种子，以确保每次生成不同的噪声
    # 这样可以更真实地模拟实际情况

    if complex_noise:
        # 只使用纯高斯噪声，确保信噪比不低于12dB
        num_samples = len(f_perfect)

        # 生成标准高斯白噪声
        white_noise_f = np.random.normal(0, freq_accuracy, num_samples)
        white_noise_phi = np.random.normal(0, phi_accuracy, num_samples)

        # 直接添加高斯噪声到完美测量值
        f_noisy = f_perfect + white_noise_f
        phi_noisy = phi_perfect + white_noise_phi

        # 只在详细模式下打印信息
        if verbose:
            print("使用纯高斯噪声模型，无多径和散射效应")
    else:
        # Simple white Gaussian noise
        f_noisy = f_perfect + np.random.normal(0, freq_accuracy, len(f_perfect))
        phi_noisy = phi_perfect + np.random.normal(0, phi_accuracy, len(phi_perfect))

    return f_noisy, phi_noisy, freq_accuracy, phi_accuracy

def build_neural_model(with_traditional_input=False):
    """构建增强的神经网络模型，专注于稳健性和异常值抵抗力"""
    # 输入层
    measurement_input = Input(shape=(2,), name='measurement_input')
    platform_input = Input(shape=(6,), name='platform_input')

    # 可选：添加传统方法的结果作为额外输入
    if with_traditional_input:
        traditional_input = Input(shape=(4,), name='traditional_input')
        combined = Concatenate()([measurement_input, platform_input, traditional_input])
    else:
        combined = Concatenate()([measurement_input, platform_input])

    # 添加噪声层，增强鲁棒性
    combined_noisy = GaussianNoise(0.01)(combined)

    # 主干网络
    x = Dense(64, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-4))(combined_noisy)
    x = BatchNormalization()(x)
    x = Dropout(0.2)(x)

    x = Dense(64, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-4))(x)
    x = BatchNormalization()(x)
    x = Dropout(0.2)(x)

    # XZ分支
    xz_branch = Dense(32, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-4))(x)
    xz_branch = BatchNormalization()(xz_branch)
    xz_branch = Dropout(0.1)(xz_branch)

    # Y分支 - 特别关注
    y_branch = Dense(32, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-4))(x)
    y_branch = BatchNormalization()(y_branch)
    y_branch = Dropout(0.2)(y_branch)

    y_branch = Dense(16, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-4))(y_branch)
    y_branch = BatchNormalization()(y_branch)

    # 频率分支
    freq_branch = Dense(16, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-4))(x)
    freq_branch = BatchNormalization()(freq_branch)

    # 输出
    x_output = Dense(1, name='x_output')(xz_branch)
    y_output = Dense(1, activation='tanh', name='y_output')(y_branch)
    z_output = Dense(1, name='z_output')(xz_branch)
    freq_output = Dense(1, name='freq_output')(freq_branch)

    # 合并位置输出
    position_output = Concatenate(name='position_output')([x_output, y_output, z_output, freq_output])

    # 置信度输出
    confidence_branch = Dense(16, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-4))(x)
    confidence_branch = BatchNormalization()(confidence_branch)
    confidence_output = Dense(1, activation='sigmoid', name='confidence_output')(confidence_branch)

    # 构建模型 - 根据是否有传统方法输入选择不同的输入
    if with_traditional_input:
        model = Model(
            inputs=[measurement_input, platform_input, traditional_input],
            outputs={'position_output': position_output, 'confidence_output': confidence_output}
        )
    else:
        model = Model(
            inputs=[measurement_input, platform_input],
            outputs={'position_output': position_output, 'confidence_output': confidence_output}
        )

    # 使用固定学习率，让ReduceLROnPlateau可以动态调整
    initial_learning_rate = 0.001

    # 使用Adam优化器
    optimizer = tf.keras.optimizers.Adam(
        learning_rate=initial_learning_rate,
        beta_1=0.9,
        beta_2=0.999,
        epsilon=1e-7
    )

    # 编译模型 - 使用多任务损失，调整权重
    # 使用高级Adam优化器配置，带权重衰减和梯度裁剪
    optimizer = tf.keras.optimizers.Adam(
        learning_rate=0.001,
        beta_1=0.9,
        beta_2=0.999,
        epsilon=1e-7,
        amsgrad=True  # 使用AMSGrad变体，提高稳定性
    )

    # 使用更好的损失函数
    # 定义Huber损失函数，对异常值更加鲁棒
    def huber_loss(y_true, y_pred, delta=1.0):
        error = y_true - y_pred
        abs_error = tf.abs(error)
        quadratic = tf.minimum(abs_error, delta)
        linear = abs_error - quadratic
        return 0.5 * tf.square(quadratic) + delta * linear

    # 编译模型
    model.compile(
        optimizer=optimizer,
        loss={
            'position_output': huber_loss,  # 使用Huber损失，对异常值更加鲁棒
            'confidence_output': 'binary_crossentropy'
        },
        loss_weights={
            'position_output': 1.0,
            'confidence_output': 0.3  # 置信度损失的权重
        },
        metrics={
            'position_output': ['mae', 'mse'],
            'confidence_output': ['accuracy']
        }
    )

    return model

def generate_training_data(num_samples=1000, snr_range=(12, 20), area_size=1000, alt_range=(0, 100)):
    """生成神经网络训练数据"""
    print(f"正在生成{num_samples}个训练样本...")

    # 随机化参数
    g_values = np.random.uniform(1.5, 2.5, num_samples)
    T_values = np.random.uniform(8, 12, num_samples)
    del_T = 0.1
    alt_kft = np.random.uniform(8, 12, num_samples)
    vel = np.random.uniform(180, 220, num_samples)
    fo = 1e9

    X_measurements = []
    X_platform = []
    Y_positions = []
    Y_confidence = []

    for i in range(num_samples):
        if i % 100 == 0:
            print(f"生成样本: {i}/{num_samples}")

        # 随机生成目标位置
        if np.random.random() < 0.7:
            x_true = np.random.uniform(-area_size, area_size)

            # Y轴分布
            y_distribution = np.random.choice(['positive', 'negative', 'zero'], p=[0.4, 0.4, 0.2])
            if y_distribution == 'positive':
                y_true = np.random.uniform(0, area_size)
            elif y_distribution == 'negative':
                y_true = np.random.uniform(-area_size, 0)
            else:
                y_true = np.random.uniform(-area_size/10, area_size/10)

            z_true = np.random.uniform(*alt_range)
        else:
            x_true = np.random.uniform(-2*area_size, 2*area_size)

            # 极端Y轴分布
            y_distribution = np.random.choice(['extreme_positive', 'extreme_negative', 'moderate'], p=[0.4, 0.4, 0.2])
            if y_distribution == 'extreme_positive':
                y_true = np.random.uniform(area_size/2, 2*area_size)
            elif y_distribution == 'extreme_negative':
                y_true = np.random.uniform(-2*area_size, -area_size/2)
            else:
                y_true = np.random.uniform(-area_size, area_size)

            z_true = np.random.uniform(0, 200)

        fo_true = fo * np.random.uniform(0.99, 1.01)
        p_true = np.array([x_true, y_true, z_true, fo_true])

        # 随机SNR (≥12dB)
        snr_db = max(12, np.random.uniform(*snr_range))

        # 生成平台轨迹
        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g_values[i], T_values[i], del_T, alt_kft[i], vel[i])
        Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

        # 生成带噪声的测量值
        f_noisy, phi_noisy, _, _ = generate_noisy_data(
            p_true, Plat_Nav_Data, mu_vect, fo, snr_db, complex_noise=True
        )

        # 提取每个时间点的测量值和平台数据
        for j in range(len(Px)):
            measurement = np.array([f_noisy[j], phi_noisy[j]])
            platform = np.array([Px[j], Py[j], Pz[j], Vx[j], Vy[j], Vz[j]])

            # 计算置信度
            platform_pos = np.array([Px[j], Py[j], Pz[j]])
            target_pos = np.array([x_true, y_true, z_true])
            distance = np.sqrt(np.sum((platform_pos - target_pos)**2))
            confidence = 1.0 - 0.2 * min(1.0, distance / 2000)

            # 偶尔添加异常值
            if np.random.random() < 0.02:
                measurement += np.random.normal(0, 5.0, 2)
                confidence = 0.1

            X_measurements.append(measurement)
            X_platform.append(platform)
            Y_positions.append(p_true)
            Y_confidence.append([confidence])

    # 转换为numpy数组
    X_measurements = np.array(X_measurements)
    X_platform = np.array(X_platform)
    Y_positions = np.array(Y_positions)
    Y_confidence = np.array(Y_confidence)

    print(f"数据生成完成: {X_measurements.shape}, {X_platform.shape}, {Y_positions.shape}, {Y_confidence.shape}")

    return X_measurements, X_platform, Y_positions, Y_confidence

def train_neural_model(X_measurements, X_platform, Y_positions, Y_confidence=None, epochs=50, batch_size=64, model=None):
    """高级神经网络训练过程，增强泛化能力和稳定性"""
    # 数据归一化
    measurement_mean = X_measurements.mean(axis=0)
    measurement_std = X_measurements.std(axis=0)
    X_measurements_norm = (X_measurements - measurement_mean) / (measurement_std + 1e-8)

    platform_mean = X_platform.mean(axis=0)
    platform_std = X_platform.std(axis=0)
    X_platform_norm = (X_platform - platform_mean) / (platform_std + 1e-8)

    position_mean = Y_positions.mean(axis=0)
    position_std = Y_positions.std(axis=0)
    Y_positions_norm = (Y_positions - position_mean) / (position_std + 1e-8)

    # 如果没有提供置信度标签，则创建默认值
    if Y_confidence is None:
        Y_confidence = np.ones((len(Y_positions), 1))
    elif len(Y_confidence.shape) == 1:
        # 确保置信度是二维数组，形状为 (samples, 1)
        Y_confidence = Y_confidence.reshape(-1, 1)

    # 数据分割 - 使用分层抽样确保各种情况都有代表
    # 根据位置将数据分成几个区域
    position_bins = 5
    x_bins = np.linspace(Y_positions[:, 0].min(), Y_positions[:, 0].max(), position_bins+1)
    y_bins = np.linspace(Y_positions[:, 1].min(), Y_positions[:, 1].max(), position_bins+1)

    x_indices = np.digitize(Y_positions[:, 0], x_bins) - 1
    y_indices = np.digitize(Y_positions[:, 1], y_bins) - 1

    # 组合成区域索引
    region_indices = x_indices * position_bins + y_indices

    # 分层抽样
    from sklearn.model_selection import StratifiedShuffleSplit

    try:
        splitter = StratifiedShuffleSplit(n_splits=1, test_size=0.2, random_state=42)
        for train_idx, val_idx in splitter.split(X_measurements, region_indices):
            break
    except:
        # 如果分层抽样失败，回退到随机抽样
        indices = np.random.permutation(len(X_measurements_norm))
        train_idx, val_idx = indices[:int(0.8*len(indices))], indices[int(0.8*len(indices)):]

    train_measurements = X_measurements_norm[train_idx]
    train_platform = X_platform_norm[train_idx]
    train_positions = Y_positions_norm[train_idx]
    train_confidence = Y_confidence[train_idx]

    val_measurements = X_measurements_norm[val_idx]
    val_platform = X_platform_norm[val_idx]
    val_positions = Y_positions_norm[val_idx]
    val_confidence = Y_confidence[val_idx]

    # 如果没有提供模型，则构建新模型
    if model is None:
        model = build_neural_model()

    # 高级训练回调函数
    callbacks = [
        # 早停策略，监控验证损失，容忍25轮没有改进
        EarlyStopping(
            monitor='val_position_output_loss',  # 主要关注位置预测损失
            patience=30,  # 增加耐心
            restore_best_weights=True,
            verbose=1,
            mode='min'  # 明确指定我们要最小化损失
        ),
        # 动态学习率调整，验证损失10轮没有改进则降低学习率
        ReduceLROnPlateau(
            monitor='val_position_output_loss',
            factor=0.6,  # 更温和的学习率降低
            patience=15,  # 增加耐心
            min_lr=1e-6,
            verbose=1,
            mode='min'  # 明确指定我们要最小化损失
        ),
        # 保存最佳模型权重
        ModelCheckpoint(
            'best_location_model.weights.h5',
            save_best_only=True,
            save_weights_only=True,  # 只保存权重，避免序列化问题
            monitor='val_position_output_loss',
            verbose=1,
            mode='min'  # 明确指定我们要最小化损失
        ),
        # 保存完整模型 - 使用.keras格式
        ModelCheckpoint(
            'best_location_model.keras',
            save_best_only=True,
            save_weights_only=False,  # 保存完整模型
            monitor='val_position_output_loss',
            verbose=1,
            mode='min'
        ),
        # TensorBoard可视化
        tf.keras.callbacks.TensorBoard(
            log_dir='./logs',
            histogram_freq=1,
            write_graph=True
        )
    ]

    # 使用更先进的训练方法
    print(f"开始训练高级神经网络模型，共{epochs}轮...")

    # 1. 首先进行预热训练 - 只训练位置输出，帮助模型稳定
    print("开始预热训练阶段 (仅位置预测)...")

    # 构建一个简单的预热模型
    # 输入层
    warmup_measurement_input = Input(shape=(2,), name='warmup_measurement_input')
    warmup_platform_input = Input(shape=(6,), name='warmup_platform_input')

    # 处理测量数据分支
    x1 = Dense(128, activation='relu')(warmup_measurement_input)
    x1 = BatchNormalization()(x1)
    x1 = Dense(256, activation='relu')(x1)
    x1 = BatchNormalization()(x1)

    # 处理平台数据分支
    x2 = Dense(128, activation='relu')(warmup_platform_input)
    x2 = BatchNormalization()(x2)
    x2 = Dense(256, activation='relu')(x2)
    x2 = BatchNormalization()(x2)

    # 合并两个分支
    combined = Concatenate()([x1, x2])

    # 深度网络
    x = Dense(512, activation='relu')(combined)
    x = BatchNormalization()(x)
    x = Dropout(0.2)(x)

    x = Dense(256, activation='relu')(x)
    x = BatchNormalization()(x)

    # 输出层 - 只有位置输出
    warmup_output = Dense(4, name='warmup_output')(x)

    # 构建预热模型
    warmup_model = Model(
        inputs=[warmup_measurement_input, warmup_platform_input],
        outputs=warmup_output
    )

    # 使用更好的优化器配置
    warmup_optimizer = tf.keras.optimizers.Adam(
        learning_rate=0.0005,
        beta_1=0.9,
        beta_2=0.999,
        epsilon=1e-7,
        amsgrad=True  # 使用AMSGrad变体，提高稳定性
    )

    warmup_model.compile(
        optimizer=warmup_optimizer,
        loss='mse',
        metrics=['mae']
    )

    # 预热训练 - 3轮
    warmup_model.fit(
        [train_measurements, train_platform],
        train_positions,
        validation_data=([val_measurements, val_platform], val_positions),
        epochs=3,  # 进一步减少预热轮数
        batch_size=batch_size,
        verbose=1,
        shuffle=True
    )

    # 2. 主训练循环 - 多任务学习
    print("\n开始主训练阶段 (位置 + 置信度预测)...")

    # 使用更高级的学习率调度器 - 余弦退火
    initial_lr = 0.001
    min_lr = 0.00001

    def cosine_annealing_with_warmup(epoch):
        # 前5个epoch为预热阶段，线性增加学习率
        if epoch < 5:
            return initial_lr * (epoch + 1) / 5

        # 之后使用余弦退火
        decay_epochs = epochs - 5
        cosine_epoch = epoch - 5
        return min_lr + 0.5 * (initial_lr - min_lr) * (1 + np.cos(np.pi * cosine_epoch / decay_epochs))

    # 添加余弦退火学习率回调
    lr_scheduler = tf.keras.callbacks.LearningRateScheduler(cosine_annealing_with_warmup)
    callbacks.append(lr_scheduler)

    # 添加提前停止回调，防止过拟合
    early_stopping = tf.keras.callbacks.EarlyStopping(
        monitor='val_position_output_loss',
        patience=15,
        restore_best_weights=True,
        verbose=1,
        mode='min'  # 明确指定我们要最小化损失
    )
    callbacks.append(early_stopping)

    # 检查模型是否需要传统方法输入
    model_inputs = model.inputs
    needs_traditional_input = len(model_inputs) > 2

    # 如果模型需要传统方法输入，生成模拟的传统方法结果
    if needs_traditional_input:
        print("模型需要传统方法输入，生成模拟数据...")
        # 为训练集生成模拟的传统方法结果（添加一些噪声的真实位置）
        train_traditional = train_positions + np.random.normal(0, 0.1, train_positions.shape)
        val_traditional = val_positions + np.random.normal(0, 0.1, val_positions.shape)

        # 主训练循环 - 带有传统方法输入
        history = model.fit(
            [train_measurements, train_platform, train_traditional],
            {
                'position_output': train_positions,
                'confidence_output': train_confidence
            },
            validation_data=(
                [val_measurements, val_platform, val_traditional],
                {
                    'position_output': val_positions,
                    'confidence_output': val_confidence
                }
            ),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1,
            shuffle=True
        )
    else:
        # 主训练循环 - 不带传统方法输入
        history = model.fit(
            [train_measurements, train_platform],
            {
                'position_output': train_positions,
                'confidence_output': train_confidence
            },
            validation_data=(
                [val_measurements, val_platform],
                {
                    'position_output': val_positions,
                    'confidence_output': val_confidence
                }
            ),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1,
            shuffle=True
            # 注意：多输出模型不支持class_weight参数
            # 我们将通过调整损失权重和样本权重来处理不平衡问题
        )

    # 计算和打印验证集指标
    if needs_traditional_input:
        val_pred_outputs = model.predict([val_measurements, val_platform, val_traditional])
    else:
        val_pred_outputs = model.predict([val_measurements, val_platform])

    # 处理不同格式的模型输出
    if isinstance(val_pred_outputs, dict):
        # 新模型格式 - 字典输出
        val_position_pred = val_pred_outputs['position_output']
    elif isinstance(val_pred_outputs, list):
        # 旧模型格式 - 列表输出
        val_position_pred = val_pred_outputs[0]
    else:
        # 单输出模型
        val_position_pred = val_pred_outputs

    # 反归一化位置预测
    val_position_pred = val_position_pred * position_std + position_mean
    val_true = Y_positions[val_idx]

    # 计算3D误差（仅使用xyz坐标）
    val_errors = np.sqrt(np.sum((val_position_pred[:,:3] - val_true[:,:3])**2, axis=1))

    print("\n============ 验证集性能指标 ============")
    print(f"平均误差: {val_errors.mean():.2f} m")
    print(f"中值误差: {np.median(val_errors):.2f} m")
    print(f"90%误差: {np.percentile(val_errors, 90):.2f} m")
    print(f"最大误差: {val_errors.max():.2f} m")

    # 计算误差在目标范围内的百分比(目标是5%或100m，取较大值)
    threshold = max(100, 0.05 * 500)  # 500m是真实位置的基准距离
    accuracy = (val_errors < threshold).mean() * 100
    print(f"误差小于{threshold:.1f}m的样本百分比: {accuracy:.2f}%")

    # 保存归一化参数，用于推理
    normalization_params = {
        'measurement_mean': measurement_mean,
        'measurement_std': measurement_std,
        'platform_mean': platform_mean,
        'platform_std': platform_std,
        'position_mean': position_mean,
        'position_std': position_std
    }

    # 保存完整模型和权重
    print("保存模型和权重...")

    try:
        # 定义文件路径
        model_path = 'best_location_model.keras'  # 使用.keras扩展名
        weights_path = 'best_location_model.weights.h5'
        norm_params_path = 'normalization_params.npy'

        # 保存完整模型 - 使用TF 2.x推荐的方式
        try:
            # 首先尝试使用新的.keras格式保存
            model.save(model_path, save_format='keras')
            print(f"完整模型已保存到: {model_path} (keras格式)")
        except Exception as e1:
            print(f"使用keras格式保存失败: {str(e1)}")
            try:
                # 如果失败，尝试使用旧的.h5格式
                model_path = 'best_location_model.h5'
                model.save(model_path, save_format='h5')
                print(f"完整模型已保存到: {model_path} (h5格式)")
            except Exception as e2:
                print(f"使用h5格式保存也失败: {str(e2)}")
                # 最后尝试只保存权重
                print("只保存模型权重...")

        # 无论如何都保存权重，作为备份
        model.save_weights(weights_path)
        print(f"模型权重已保存到: {weights_path}")

        # 保存归一化参数
        np.save(norm_params_path, normalization_params)
        print(f"归一化参数已保存到: {norm_params_path}")

        # 保存模型结构的JSON描述，作为另一种备份
        model_json = model.to_json()
        with open("model_structure.json", "w") as json_file:
            json_file.write(model_json)
        print("模型结构已保存到: model_structure.json")

        # 验证文件是否存在
        import os
        if os.path.exists(model_path):
            print(f"验证: 模型文件存在，大小: {os.path.getsize(model_path)/1024/1024:.2f} MB")
        else:
            print(f"警告: 模型文件保存失败!")

        if os.path.exists(weights_path):
            print(f"验证: 权重文件存在，大小: {os.path.getsize(weights_path)/1024/1024:.2f} MB")
        else:
            print(f"警告: 权重文件保存失败!")

        if os.path.exists(norm_params_path):
            print(f"验证: 归一化参数文件存在，大小: {os.path.getsize(norm_params_path)/1024:.2f} KB")
        else:
            print(f"警告: 归一化参数文件保存失败!")

    except Exception as e:
        print(f"保存模型时出错: {str(e)}")

    # 绘制训练历史
    plt.figure(figsize=(15, 5))

    # 绘制总损失
    plt.subplot(1, 3, 1)
    plt.plot(history.history['loss'], label='训练总损失')
    plt.plot(history.history['val_loss'], label='验证总损失')
    plt.title('模型总损失曲线', fontsize=13)
    plt.xlabel('训练轮次', fontsize=12)
    plt.ylabel('损失值', fontsize=12)
    plt.legend()

    # 绘制位置输出损失
    plt.subplot(1, 3, 2)
    plt.plot(history.history['position_output_loss'], label='训练位置损失')
    plt.plot(history.history['val_position_output_loss'], label='验证位置损失')
    plt.title('位置预测损失', fontsize=13)
    plt.xlabel('训练轮次', fontsize=12)
    plt.ylabel('MSE', fontsize=12)
    plt.legend()

    # 添加误差分布直方图
    plt.subplot(1, 3, 3)
    plt.hist(val_errors, bins=30, color='skyblue', edgecolor='black', alpha=0.7)
    plt.axvline(x=threshold, color='red', linestyle='--', label=f'目标阈值: {threshold:.1f}m')
    plt.title('验证集误差分布', fontsize=13)
    plt.xlabel('3D误差 (米)', fontsize=12)
    plt.ylabel('样本数量', fontsize=12)
    plt.legend()

    plt.tight_layout()
    plt.savefig('training_history.png', dpi=300, bbox_inches='tight')

    # 额外绘制置信度预测性能
    plt.figure(figsize=(12, 5))

    # 置信度损失
    plt.subplot(1, 2, 1)
    plt.plot(history.history['confidence_output_loss'], label='训练置信度损失')
    plt.plot(history.history['val_confidence_output_loss'], label='验证置信度损失')
    plt.title('置信度预测损失', fontsize=13)
    plt.xlabel('训练轮次', fontsize=12)
    plt.ylabel('BCE损失', fontsize=12)
    plt.legend()

    # 置信度准确率
    plt.subplot(1, 2, 2)
    plt.plot(history.history['confidence_output_accuracy'], label='训练置信度准确率')
    plt.plot(history.history['val_confidence_output_accuracy'], label='验证置信度准确率')
    plt.title('置信度预测准确率', fontsize=13)
    plt.xlabel('训练轮次', fontsize=12)
    plt.ylabel('准确率', fontsize=12)
    plt.legend()

    plt.tight_layout()
    plt.savefig('confidence_metrics.png', dpi=300, bbox_inches='tight')

    return model, normalization_params

def neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params, traditional_result=None):
    """增强的神经网络预测函数，专注于异常值检测和稳健性"""
    # 准备输入数据
    measurements = np.vstack((f_obs, phi_obs)).T
    platform = Plat_Nav_Data[:6, :].T

    # 1. 预处理 - 检测和过滤异常测量值
    f_median = np.median(f_obs)
    phi_median = np.median(phi_obs)
    f_mad = np.median(np.abs(f_obs - f_median)) * 1.4826
    phi_mad = np.median(np.abs(phi_obs - phi_median)) * 1.4826

    # 标记异常值
    f_outliers = np.abs(f_obs - f_median) > 3.0 * f_mad
    phi_outliers = np.abs(phi_obs - phi_median) > 3.0 * phi_mad
    combined_outliers = f_outliers | phi_outliers

    # 过滤异常值
    if np.sum(combined_outliers) > 0:
        print(f"检测到{np.sum(combined_outliers)}个异常测量值，已过滤")
        good_indices = ~combined_outliers
        if np.sum(good_indices) >= 5:
            measurements = measurements[good_indices]
            platform = platform[good_indices]

    # 归一化
    measurements_norm = (measurements - normalization_params['measurement_mean']) / normalization_params['measurement_std']
    platform_norm = (platform - normalization_params['platform_mean']) / normalization_params['platform_std']

    # 2. 预测
    if traditional_result is not None:
        # 使用传统方法结果作为额外输入
        traditional_input = np.tile(traditional_result, (len(measurements), 1))
        traditional_norm = (traditional_input - normalization_params['position_mean']) / normalization_params['position_std']
        model_outputs = model.predict([measurements_norm, platform_norm, traditional_norm], verbose=0)
    else:
        model_outputs = model.predict([measurements_norm, platform_norm], verbose=0)

    # 处理模型输出
    if isinstance(model_outputs, dict):
        position_norm = model_outputs['position_output']
        confidence = model_outputs['confidence_output']
    elif isinstance(model_outputs, list):
        position_norm = model_outputs[0]
        confidence = model_outputs[1]
    else:
        position_norm = model_outputs
        confidence = np.ones((position_norm.shape[0], 1))

    # 3. 反归一化
    position_pred = position_norm * normalization_params['position_std'] + normalization_params['position_mean']

    # 4. 过滤异常预测
    median_pred = np.median(position_pred, axis=0)
    distances = np.sqrt(np.sum((position_pred[:, :3] - median_pred[:3])**2, axis=1))

    dist_median = np.median(distances)
    dist_mad = np.median(np.abs(distances - dist_median)) * 1.4826

    pred_outliers = distances > dist_median + 2.0 * dist_mad

    if np.sum(pred_outliers) > 0 and np.sum(~pred_outliers) >= 5:
        print(f"检测到{np.sum(pred_outliers)}个异常预测，已过滤")
        position_pred = position_pred[~pred_outliers]
        confidence = confidence[~pred_outliers]

    # 5. 限制Y轴预测范围
    y_scale = 50.0
    for i in range(len(position_pred)):
        if abs(position_pred[i, 1]) > y_scale:
            position_pred[i, 1] = np.sign(position_pred[i, 1]) * y_scale

    # 6. 预测融合
    sorted_by_conf = np.argsort(-confidence.flatten())
    top_indices = sorted_by_conf[:max(5, len(sorted_by_conf)//2)]
    top_positions = position_pred[top_indices]
    final_pos = np.median(top_positions, axis=0)

    # 7. 应用物理约束
    if final_pos[2] < 0:
        final_pos[2] = 0

    if final_pos[3] < 0.9e9 or final_pos[3] > 1.1e9:
        final_pos[3] = 1e9

    # 8. 使用传统方法约束
    if traditional_result is not None:
        trad_diff = np.abs(final_pos[:3] - traditional_result[:3])
        if np.any(trad_diff > 100):
            print("警告: 预测与传统方法差异过大")
            for i in range(3):
                if trad_diff[i] > 100:
                    print(f"维度 {i} 差异过大 ({trad_diff[i]:.2f}m)，使用传统方法的值")
                    final_pos[i] = traditional_result[i]

    return final_pos

def run_doppler_aoa_with_nn(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L,
                         model=None, normalization_params=None, train_model=False):
    """使用神经网络模型辅助的多普勒-AoA定位"""
    # 生成轨迹数据
    Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
    t = np.arange(0, len(Px)) * del_T  # 用于绘图，不影响计算
    Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

    # 生成带噪声的测量值 - 使用复杂噪声模型
    f_obs, phi_obs, freq_accuracy, phi_accuracy = generate_noisy_data(
        p_true, Plat_Nav_Data, mu_vect, fo, snr_db, complex_noise=True)

    print("生成的轨迹点数:", len(Px))
    print("真实目标位置:", p_true[:3])
    print("初始估计:", p_est_init[:3])

    # 先运行传统方法获取结果，用于辅助神经网络
    print("运行传统方法获取基准结果...")
    try:
        # 运行优化
        result = least_squares(
            lambda params: residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy),
            p_est_init,
            method='trf',  # Trust Region Reflective算法
            ftol=1e-10,
            xtol=1e-10,
            gtol=1e-10,
            max_nfev=5000,
            verbose=1
        )
        p_est_traditional = result.x
        traditional_error = np.linalg.norm(p_est_traditional[:3] - p_true[:3])
        print(f"传统方法误差: {traditional_error:.2f} m")
    except Exception as e:
        print(f"传统方法优化失败: {str(e)}")
        p_est_traditional = p_est_init
        traditional_error = np.linalg.norm(p_est_traditional[:3] - p_true[:3])

    # 是否使用传统方法辅助
    use_traditional_input = True

    # 检查模型文件是否存在的函数
    def check_model_files():
        import os
        # 首先检查.keras格式的模型文件
        keras_model_path = 'best_location_model.keras'
        h5_model_path = 'best_location_model.h5'
        weights_path = 'best_location_model.weights.h5'
        norm_params_path = 'normalization_params.npy'

        files_exist = True
        model_path = None

        # 优先检查.keras格式
        if os.path.exists(keras_model_path):
            file_size = os.path.getsize(keras_model_path) / (1024 * 1024)
            print(f"找到keras格式模型文件: {keras_model_path} (大小: {file_size:.2f} MB)")
            if file_size < 0.1:  # 文件太小，可能损坏
                print(f"警告: 模型文件可能损坏，大小仅 {file_size:.2f} MB")
            else:
                model_path = keras_model_path
        # 如果没有找到.keras格式，检查.h5格式
        elif os.path.exists(h5_model_path):
            file_size = os.path.getsize(h5_model_path) / (1024 * 1024)
            print(f"找到h5格式模型文件: {h5_model_path} (大小: {file_size:.2f} MB)")
            if file_size < 0.1:  # 文件太小，可能损坏
                print(f"警告: 模型文件可能损坏，大小仅 {file_size:.2f} MB")
            else:
                model_path = h5_model_path
        else:
            print(f"警告: 未找到任何模型文件 (.keras 或 .h5)")

        # 如果没有找到任何有效的模型文件
        if model_path is None:
            files_exist = False
            # 默认使用.keras路径，即使文件不存在
            model_path = keras_model_path

        # 检查权重文件
        if os.path.exists(weights_path):
            file_size = os.path.getsize(weights_path) / (1024 * 1024)
            print(f"找到权重文件: {weights_path} (大小: {file_size:.2f} MB)")
            if file_size < 0.1:  # 文件太小，可能损坏
                print(f"警告: 权重文件可能损坏，大小仅 {file_size:.2f} MB")
                files_exist = False
        else:
            print(f"警告: 权重文件不存在: {weights_path}")
            files_exist = False

        # 检查归一化参数文件
        if os.path.exists(norm_params_path):
            file_size = os.path.getsize(norm_params_path) / 1024
            print(f"找到归一化参数文件: {norm_params_path} (大小: {file_size:.2f} KB)")
            if file_size < 0.1:  # 文件太小，可能损坏
                print(f"警告: 归一化参数文件可能损坏，大小仅 {file_size:.2f} KB")
                files_exist = False
        else:
            print(f"警告: 归一化参数文件不存在: {norm_params_path}")
            files_exist = False

        return files_exist, model_path, weights_path, norm_params_path

    # 尝试加载已有模型
    if not train_model and model is None:
        print("尝试加载已保存的模型...")

        # 检查模型文件是否存在
        files_exist, model_path, weights_path, norm_params_path = check_model_files()

        if not files_exist:
            print("必要的模型文件不存在或已损坏，将进行模型训练")
            train_model = True
        else:
            try:
                # 定义Huber损失函数，对异常值更加鲁棒
                def huber_loss(y_true, y_pred, delta=1.0):
                    error = y_true - y_pred
                    abs_error = tf.abs(error)
                    quadratic = tf.minimum(abs_error, delta)
                    linear = abs_error - quadratic
                    return 0.5 * tf.square(quadratic) + delta * linear

                # 尝试加载完整模型
                try:
                    print(f"尝试加载完整模型: {model_path}")
                    # 明确指定custom_objects，确保huber_loss函数被正确识别
                    custom_objects = {
                        'huber_loss': huber_loss,
                        # 添加其他可能需要的自定义对象
                        'tf': tf
                    }
                    model = tf.keras.models.load_model(model_path, custom_objects=custom_objects)
                    print("成功加载完整模型")
                except Exception as e:
                    print(f"加载完整模型失败: {str(e)}")
                    print("尝试加载模型权重...")

                    # 如果完整模型加载失败，尝试加载权重
                    model = build_neural_model(with_traditional_input=use_traditional_input)

                    # 编译模型
                    model.compile(
                        optimizer='adam',
                        loss={
                            'position_output': huber_loss,
                            'confidence_output': 'binary_crossentropy'
                        },
                        loss_weights={
                            'position_output': 1.0,
                            'confidence_output': 0.3
                        },
                        metrics={
                            'position_output': 'mse',
                            'confidence_output': 'accuracy'
                        }
                    )

                    # 加载权重
                    model.load_weights(weights_path)
                    print("成功加载模型权重")

                # 加载归一化参数
                normalization_params = np.load(norm_params_path, allow_pickle=True).item()
                print("成功加载归一化参数")

            except Exception as e:
                print(f"加载模型失败: {str(e)}")
                print("将进行模型训练...")
                train_model = True  # 如果加载失败，强制训练

    # 如果需要训练模型
    if train_model:
        print("训练神经网络模型...")
        # 使用较少样本和纯高斯噪声生成训练数据
        X_measurements, X_platform, Y_positions, Y_confidence = generate_training_data(
            num_samples=1000, snr_range=(12, 20))  # 使用1000个样本加快训练

        # 创建模型 - 根据是否使用传统方法辅助选择不同的模型
        model = build_neural_model(with_traditional_input=use_traditional_input)

        # 使用改进的训练函数
        model, normalization_params = train_neural_model(
            X_measurements, X_platform, Y_positions, Y_confidence, epochs=50, batch_size=64,
            model=model)  # 使用50轮加快训练

    # 如果仍然没有模型，则使用传统方法
    if model is None:
        try:
            # 运行优化
            result = least_squares(
                lambda params: residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy),
                p_est_init,
                method='trf',  # Trust Region Reflective算法
                ftol=1e-10,
                xtol=1e-10,
                gtol=1e-10,
                max_nfev=5000,
                verbose=1
            )
            p_est = result.x
            method_name = "传统最小二乘法"
        except Exception as e:
            print(f"优化失败: {str(e)}")
            # 回退到简单估计
            p_est = p_est_init
            method_name = "传统方法（失败回退）"
    else:
        # 使用改进的神经网络模型预测 - 根据是否使用传统方法辅助选择不同的预测方式
        if use_traditional_input:
            p_est = neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params,
                                           traditional_result=p_est_traditional)
            method_name = "传统方法辅助的神经网络模型"
        else:
            p_est = neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params)
            method_name = "纯神经网络模型"

    # 打印最终结果
    error_vector = p_est[:3] - p_true[:3]
    error_distance = np.linalg.norm(error_vector)

    print(f"\n{method_name}最终估计位置:", p_est[:3])
    print("估计误差:", error_distance, "米")

    # 计算CRLB（仅对传统方法）
    if "传统" in method_name:
        CRLB = calculate_crlb(p_est, Plat_Nav_Data, mu_vect, fo, L, freq_accuracy, phi_accuracy)
        C_xy = CRLB[:2, :2]
        print("位置CRLB对角线:", np.diag(CRLB[:3, :3]))
    else:
        # 对神经网络方法，我们使用训练集的验证误差作为置信区间
        C_xy = np.eye(2) * (error_distance/2)**2

    # 绘制结果
    plt.figure(figsize=(12, 10))

    # 配置网格
    plt.grid(True, linestyle='--', alpha=0.7)

    # 绘制平台轨迹
    plt.plot(Px, Py, '-', color='#1E88E5', linewidth=2, label='平台路径')

    # 绘制估计位置
    plt.scatter(p_est[0], p_est[1], s=120, marker='o', color='#1E88E5',
             edgecolor='white', linewidth=2, label='估计位置', zorder=3)

    # 绘制真实位置
    plt.scatter(p_true[0], p_true[1], s=150, marker='x', color='#D81B60',
             linewidth=2.5, label='真实位置', zorder=3)

    # 尝试绘制误差椭圆
    try:
        ellipse = draw_error_ellipse(plt.gca(), p_true[:2], C_xy, k=2,
                           edgecolor='#D81B60', facecolor='none',
                           linewidth=2, alpha=0.8, zorder=2)
        # 为图例添加代理
        from matplotlib.lines import Line2D
        ellipse_proxy = Line2D([0], [0], color='#D81B60', lw=2)
        plt.legend([
            Line2D([0], [0], color='#1E88E5', lw=2),
            Line2D([0], [0], marker='o', color='#1E88E5', markersize=10, markeredgecolor='white'),
            Line2D([0], [0], marker='x', color='#D81B60', markersize=10, linestyle=''),
            ellipse_proxy
        ], ['平台路径', '估计位置', '真实位置', '95%置信区间'],
        fontsize=12, loc='upper right', frameon=True, framealpha=0.9)
    except Exception as e:
        print(f"警告: 误差椭圆绘制失败: {str(e)}")
        plt.legend(fontsize=12)

    # 设置标题和标签
    plt.title(f'发射机位置估计 (方法: {method_name}, 误差: {error_distance:.2f}m)', fontsize=14)
    plt.xlabel('X位置 (米)', fontsize=12)
    plt.ylabel('Y位置 (米)', fontsize=12)

    # 确保坐标轴比例相等
    plt.axis('equal')

    # 保存高质量图像
    plt.tight_layout()
    plt.savefig('location_estimation.png', dpi=300, bbox_inches='tight')

    # 打印详细结果
    print("\n多普勒-AoA估计结果:")
    print("-------------------------")
    print(f"X轴误差: {error_vector[0]:.2f} m")
    print(f"Y轴误差: {error_vector[1]:.2f} m")
    print(f"Z轴误差: {error_vector[2]:.2f} m")
    print(f"3D误差: {error_distance:.2f} m")
    print(f"定位方法: {method_name}")

    return p_est

def residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy):
    """最小二乘优化的残差函数"""
    # 计算预期测量值
    f_exp, phi_exp = calculate_measurements(params, Plat_Nav_Data, mu_vect, fo)

    # 计算加权残差
    res_dop = (f_exp - f_obs) / freq_accuracy
    res_aoa = (phi_exp - phi_obs) / phi_accuracy

    # 合并残差
    res = np.concatenate([res_dop, res_aoa])

    return res

def calculate_crlb(params, Plat_Nav_Data, mu_vect, fo, L, freq_accuracy, phi_accuracy):
    """Calculate Cramer-Rao Lower Bound (CRLB)"""
    # Parse navigation data
    Px = Plat_Nav_Data[0, :]
    Py = Plat_Nav_Data[1, :]
    Pz = Plat_Nav_Data[2, :]
    Vx = Plat_Nav_Data[3, :]
    Vy = Plat_Nav_Data[4, :]
    Vz = Plat_Nav_Data[5, :]

    # Number of measurements
    N = len(Px)

    # Calculate Jacobian matrix
    J = np.zeros((2*N, 4))

    # Use finite difference to calculate Jacobian
    eps = 1e-6
    for i in range(4):
        params_plus = params.copy()
        params_plus[i] += eps

        params_minus = params.copy()
        params_minus[i] -= eps

        f_plus, phi_plus = calculate_measurements(params_plus, Plat_Nav_Data, mu_vect, fo)
        f_minus, phi_minus = calculate_measurements(params_minus, Plat_Nav_Data, mu_vect, fo)

        J[:N, i] = (f_plus - f_minus) / (2*eps) / freq_accuracy
        J[N:, i] = (phi_plus - phi_minus) / (2*eps) / phi_accuracy

    # Calculate Fisher Information Matrix
    FIM = J.T @ J

    # Add small regularization for numerical stability
    FIM = FIM + 1e-10 * np.eye(4)

    # Calculate CRLB
    try:
        CRLB = np.linalg.inv(FIM)
        return CRLB
    except:
        print("Warning: Singular FIM, cannot calculate CRLB")
        return np.eye(4) * 1000  # Return a default value

def draw_error_ellipse(ax, mean, cov, k=1, **kwargs):
    """Draw error ellipse"""
    # Ensure covariance matrix is symmetric
    cov = (cov + cov.T) / 2

    # Calculate eigenvalues and eigenvectors
    try:
        eigvals, eigvecs = np.linalg.eigh(cov)

        # Ensure eigenvalues are positive
        eigvals = np.maximum(eigvals, 1e-10)

        # Calculate ellipse parameters
        width = 2 * k * np.sqrt(eigvals[0])
        height = 2 * k * np.sqrt(eigvals[1])

        # Angle in degrees
        angle = np.degrees(np.arctan2(eigvecs[1, 0], eigvecs[0, 0]))

        # Create ellipse
        ellipse = Ellipse(xy=mean, width=width, height=height,
                         angle=angle, **kwargs)
        ax.add_patch(ellipse)
        return ellipse
    except:
        print("Warning: Could not compute error ellipse")
        return None

def compare_methods(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L):
    """比较传统方法和高级神经网络方法的性能"""
    # 生成轨迹数据
    Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
    t = np.arange(0, len(Px)) * del_T  # 用于绘图，不影响计算
    Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

    # 生成带噪声的测量值 - 使用复杂噪声模型
    f_obs, phi_obs, freq_accuracy, phi_accuracy = generate_noisy_data(
        p_true, Plat_Nav_Data, mu_vect, fo, snr_db, complex_noise=True)

    # 运行传统方法
    try:
        result = least_squares(
            lambda params: residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy),
            p_est_init,
            method='trf',
            ftol=1e-10,
            xtol=1e-10,
            gtol=1e-10,
            max_nfev=5000,
            verbose=0
        )
        p_est_traditional = result.x
        trad_error = np.linalg.norm(p_est_traditional[:3] - p_true[:3])
    except Exception as e:
        print(f"传统方法优化失败: {str(e)}")
        p_est_traditional = p_est_init
        trad_error = np.linalg.norm(p_est_traditional[:3] - p_true[:3])

    # 加载神经网络模型
    try:
        print("尝试加载预训练模型...")

        # 定义Huber损失函数，对异常值更加鲁棒
        def huber_loss(y_true, y_pred, delta=1.0):
            error = y_true - y_pred
            abs_error = tf.abs(error)
            quadratic = tf.minimum(abs_error, delta)
            linear = abs_error - quadratic
            return 0.5 * tf.square(quadratic) + delta * linear

        # 首先尝试加载完整模型（.keras格式）
        try:
            if os.path.exists('best_location_model.keras'):
                print("尝试加载.keras格式模型...")
                # 明确指定custom_objects，确保huber_loss函数被正确识别
                custom_objects = {
                    'huber_loss': huber_loss,
                    # 添加其他可能需要的自定义对象
                    'tf': tf
                }
                model = tf.keras.models.load_model('best_location_model.keras', custom_objects=custom_objects)
                print("成功加载.keras格式模型")
            elif os.path.exists('best_location_model.h5'):
                print("尝试加载.h5格式模型...")
                # 明确指定custom_objects，确保huber_loss函数被正确识别
                custom_objects = {
                    'huber_loss': huber_loss,
                    # 添加其他可能需要的自定义对象
                    'tf': tf
                }
                model = tf.keras.models.load_model('best_location_model.h5', custom_objects=custom_objects)
                print("成功加载.h5格式模型")
            else:
                raise FileNotFoundError("未找到模型文件")

        except Exception as e:
            print(f"加载完整模型失败: {str(e)}")
            print("尝试加载模型权重...")

            # 如果完整模型加载失败，尝试加载权重
            model = build_neural_model()

            # 编译模型
            model.compile(
                optimizer='adam',
                loss={
                    'position_output': huber_loss,  # 使用Huber损失，对异常值更加鲁棒
                    'confidence_output': 'binary_crossentropy'
                },
                loss_weights={
                    'position_output': 1.0,
                    'confidence_output': 0.3
                },
                metrics={
                    'position_output': 'mse',
                    'confidence_output': 'accuracy'
                }
            )

            # 加载权重
            model.load_weights('best_location_model.weights.h5')
            print("成功加载模型权重")

        # 加载归一化参数
        normalization_params = np.load('normalization_params.npy', allow_pickle=True).item()

        # 检查模型是否需要传统方法输入
        model_inputs = model.inputs
        needs_traditional_input = len(model_inputs) > 2

        # 使用神经网络预测
        if needs_traditional_input:
            # 如果需要传统方法输入，先运行传统方法
            print("模型需要传统方法输入，使用传统方法辅助...")
            p_est_nn = neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params,
                                               traditional_result=p_est_traditional)
        else:
            p_est_nn = neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params)

        nn_error = np.linalg.norm(p_est_nn[:3] - p_true[:3])
    except Exception as e:
        print(f"找不到预训练模型或预测失败: {str(e)}")
        print("将进行模型训练...")

        # 是否使用传统方法辅助
        use_traditional_input = True

        # 训练高级神经网络模型
        X_measurements, X_platform, Y_positions, Y_confidence = generate_training_data(
            num_samples=1000, snr_range=(12, 20))  # 使用1000个样本加快训练

        # 创建模型 - 根据是否使用传统方法辅助选择不同的模型
        model = build_neural_model(with_traditional_input=use_traditional_input)

        # 使用改进的训练函数
        model, normalization_params = train_neural_model(
            X_measurements, X_platform, Y_positions, Y_confidence, epochs=50, batch_size=64,
            model=model)  # 使用50轮加快训练

        # 使用神经网络预测 - 根据是否使用传统方法辅助选择不同的预测方式
        if use_traditional_input:
            p_est_nn = neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params,
                                               traditional_result=p_est_traditional)
        else:
            p_est_nn = neural_network_prediction(model, f_obs, phi_obs, Plat_Nav_Data, normalization_params)

        nn_error = np.linalg.norm(p_est_nn[:3] - p_true[:3])

    # 打印比较结果
    print("\n方法比较:")
    print("-------------------------")
    print(f"传统方法误差: {trad_error:.2f} m")
    print(f"高级神经网络方法误差: {nn_error:.2f} m")

    # 计算改进百分比
    improvement = (trad_error - nn_error)/trad_error*100
    print(f"改进百分比: {improvement:.1f}%")

    # 检查是否达到目标（神经网络优于传统方法）
    if improvement > 0:
        print("✓ 目标达成: 神经网络方法优于传统方法")
        if nn_error < 100 or nn_error < 0.05 * np.linalg.norm(p_true[:3]):
            print("✓ 目标达成: 神经网络方法误差满足要求（小于100m或5%真实距离）")
        else:
            print("✗ 目标未达成: 神经网络方法误差不满足要求")
    else:
        print("✗ 目标未达成: 神经网络方法未能优于传统方法")

    # 绘制比较结果
    plt.figure(figsize=(14, 10))

    # 配置网格
    plt.grid(True, linestyle='--', alpha=0.7)

    # 绘制平台轨迹
    plt.plot(Px, Py, '-', color='#1E88E5', linewidth=2, label='平台路径')

    # 绘制传统方法估计位置
    plt.scatter(p_est_traditional[0], p_est_traditional[1], s=120, marker='o', color='#FFC107',
             edgecolor='white', linewidth=2, label=f'传统方法 ({trad_error:.2f}m)', zorder=3)

    # 绘制神经网络估计位置
    plt.scatter(p_est_nn[0], p_est_nn[1], s=120, marker='s', color='#1E88E5',
             edgecolor='white', linewidth=2, label=f'高级神经网络 ({nn_error:.2f}m)', zorder=3)

    # 绘制真实位置
    plt.scatter(p_true[0], p_true[1], s=150, marker='x', color='#D81B60',
             linewidth=2.5, label='真实位置', zorder=3)

    # 设置标题和标签
    plt.title(f'方法比较: 传统 vs 高级神经网络 (SNR: {snr_db}dB, 改进: {improvement:.1f}%)', fontsize=14)
    plt.xlabel('X位置 (米)', fontsize=12)
    plt.ylabel('Y位置 (米)', fontsize=12)

    # 确保坐标轴比例相等
    plt.axis('equal')
    plt.legend(fontsize=12)

    # 保存高质量图像
    plt.tight_layout()
    plt.savefig('method_comparison.png', dpi=300, bbox_inches='tight')

    # 额外绘制误差分析图
    plt.figure(figsize=(12, 5))

    # 误差分量比较
    plt.subplot(1, 2, 1)
    error_components = ['X误差', 'Y误差', 'Z误差', '3D误差']
    trad_errors = [
        abs(p_est_traditional[0] - p_true[0]),
        abs(p_est_traditional[1] - p_true[1]),
        abs(p_est_traditional[2] - p_true[2]),
        trad_error
    ]
    nn_errors = [
        abs(p_est_nn[0] - p_true[0]),
        abs(p_est_nn[1] - p_true[1]),
        abs(p_est_nn[2] - p_true[2]),
        nn_error
    ]

    x = np.arange(len(error_components))
    width = 0.35

    plt.bar(x - width/2, trad_errors, width, label='传统方法', color='#FFC107')
    plt.bar(x + width/2, nn_errors, width, label='高级神经网络', color='#1E88E5')

    plt.ylabel('误差 (米)', fontsize=12)
    plt.title('误差分量比较', fontsize=13)
    plt.xticks(x, error_components)
    plt.legend()

    # 误差改进百分比
    plt.subplot(1, 2, 2)
    improvements = [(trad - nn)/trad*100 for trad, nn in zip(trad_errors, nn_errors)]

    colors = ['#4CAF50' if imp > 0 else '#F44336' for imp in improvements]
    plt.bar(error_components, improvements, color=colors)
    plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)

    plt.ylabel('改进百分比 (%)', fontsize=12)
    plt.title('神经网络相对传统方法的改进', fontsize=13)

    plt.tight_layout()
    plt.savefig('error_analysis.png', dpi=300, bbox_inches='tight')

    return p_est_traditional, p_est_nn

def realtime_localization(model, normalization_params, f_obs, phi_obs, platform_data):
    """
    用于实时定位的专用函数，针对速度进行了优化

    参数:
    model - 训练好的神经网络模型
    normalization_params - 归一化参数
    f_obs - 观测的多普勒频移
    phi_obs - 观测的AoA相位
    platform_data - 平台位置和速度数据 (6xN矩阵)

    返回:
    position - 估计的发射机位置 [x, y, z, f]
    processing_time - 处理时间(秒)
    """
    import time
    start_time = time.time()

    # 准备输入数据
    measurements = np.vstack((f_obs, phi_obs)).T
    platform = platform_data.T

    # 归一化
    measurements_norm = (measurements - normalization_params['measurement_mean']) / normalization_params['measurement_std']
    platform_norm = (platform - normalization_params['platform_mean']) / normalization_params['platform_std']

    # 使用模型预测 - 批量处理以加速
    model_outputs = model.predict([measurements_norm, platform_norm], verbose=0)

    # 处理多输出模型的结果
    if isinstance(model_outputs, dict):
        # 新模型格式 - 字典输出
        position_norm = model_outputs['position_output']
    elif isinstance(model_outputs, list):
        # 旧模型格式 - 列表输出
        position_norm = model_outputs[0]
    else:
        # 单输出模型
        position_norm = model_outputs

    # 反归一化
    position_pred = position_norm * normalization_params['position_std'] + normalization_params['position_mean']

    # 快速融合 - 使用加权平均
    median_pos = np.median(position_pred, axis=0)

    # 结束计时
    end_time = time.time()
    processing_time = end_time - start_time

    return median_pos, processing_time

# 添加一个测试实时定位性能的函数
def test_realtime_performance(num_tests=100):
    """测试模型实时定位性能"""
    try:
        import os  # 导入os模块
        print("加载预训练模型...")

        # 定义Huber损失函数，对异常值更加鲁棒
        def huber_loss(y_true, y_pred, delta=1.0):
            error = y_true - y_pred
            abs_error = tf.abs(error)
            quadratic = tf.minimum(abs_error, delta)
            linear = abs_error - quadratic
            return 0.5 * tf.square(quadratic) + delta * linear

        # 首先尝试加载.keras格式模型
        if os.path.exists('best_location_model.keras'):
            print("尝试加载.keras格式模型...")
            # 明确指定custom_objects，确保huber_loss函数被正确识别
            custom_objects = {
                'huber_loss': huber_loss,
                # 添加其他可能需要的自定义对象
                'tf': tf
            }
            model = tf.keras.models.load_model('best_location_model.keras', custom_objects=custom_objects)
            print("成功加载.keras格式模型")
        # 如果失败，尝试加载.h5格式模型
        elif os.path.exists('best_location_model.h5'):
            print("尝试加载.h5格式模型...")
            # 明确指定custom_objects，确保huber_loss函数被正确识别
            custom_objects = {
                'huber_loss': huber_loss,
                # 添加其他可能需要的自定义对象
                'tf': tf
            }
            model = tf.keras.models.load_model('best_location_model.h5', custom_objects=custom_objects)
            print("成功加载.h5格式模型")
        else:
            raise FileNotFoundError("未找到模型文件")

        # 加载归一化参数
        normalization_params = np.load('normalization_params.npy', allow_pickle=True).item()

        # 设置标准参数
        g = 2
        T = 10
        del_T = 0.1
        snr_db = 12
        alt_kft = 10
        vel = 200
        fo = 1e9

        # 用于存储处理时间的数组
        processing_times = []

        # 生成轨迹和测量数据
        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
        platform_data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

        # 用于存储误差数据
        errors = []
        error_x = []
        error_y = []
        error_z = []

        print("\n正在进行实时定位性能测试...")
        # 只在开始时显示一次噪声参数
        _, _, freq_accuracy, phi_accuracy = generate_noisy_data(
            np.array([0, 0, 0, fo]), platform_data, mu_vect, fo, snr_db, complex_noise=True, verbose=True)
        print("开始测试，将进行100次随机位置定位测试...\n")

        for i in range(num_tests):
            # 随机生成目标位置
            x_true = np.random.uniform(-1000, 1000)
            y_true = np.random.uniform(-1000, 1000)
            z_true = np.random.uniform(0, 200)
            p_true = np.array([x_true, y_true, z_true, fo])

            # 生成测量数据 - 使用复杂噪声模型，但不输出详细信息
            f_obs, phi_obs, _, _ = generate_noisy_data(p_true, platform_data, mu_vect, fo, snr_db, complex_noise=True, verbose=False)

            # 使用实时定位函数
            p_est, proc_time = realtime_localization(model, normalization_params, f_obs, phi_obs, platform_data)

            # 计算误差
            error_vector = p_est[:3] - p_true[:3]
            error = np.linalg.norm(error_vector)

            # 记录各个维度的误差
            errors.append(error)
            error_x.append(abs(error_vector[0]))
            error_y.append(abs(error_vector[1]))
            error_z.append(abs(error_vector[2]))

            # 记录处理时间
            processing_times.append(proc_time)

            # 打印进度
            if (i+1) % 10 == 0:
                print(f"完成: {i+1}/{num_tests}, 当前处理时间: {proc_time*1000:.2f} ms, 误差: {error:.2f} m")

        # 计算时间统计数据
        avg_time = np.mean(processing_times)
        min_time = np.min(processing_times)
        max_time = np.max(processing_times)
        std_time = np.std(processing_times)

        # 计算误差统计数据
        avg_error = np.mean(errors)
        median_error = np.median(errors)
        max_error = np.max(errors)
        min_error = np.min(errors)
        std_error = np.std(errors)

        # 计算各维度误差
        avg_error_x = np.mean(error_x)
        avg_error_y = np.mean(error_y)
        avg_error_z = np.mean(error_z)

        # 计算误差百分位数
        percentile_90 = np.percentile(errors, 90)
        percentile_95 = np.percentile(errors, 95)

        # 计算误差在不同范围内的百分比
        within_50m = np.mean(np.array(errors) < 50) * 100
        within_100m = np.mean(np.array(errors) < 100) * 100
        within_200m = np.mean(np.array(errors) < 200) * 100

        print("\n=== 实时定位性能报告 ===")
        print("\n--- 处理时间性能 ---")
        print(f"平均处理时间: {avg_time*1000:.2f} ms")
        print(f"最小处理时间: {min_time*1000:.2f} ms")
        print(f"最大处理时间: {max_time*1000:.2f} ms")
        print(f"处理时间标准差: {std_time*1000:.2f} ms")
        print(f"处理频率: {1/avg_time:.2f} Hz")

        print("\n--- 定位误差性能 ---")
        print(f"平均误差: {avg_error:.2f} m")
        print(f"中位数误差: {median_error:.2f} m")
        print(f"最小误差: {min_error:.2f} m")
        print(f"最大误差: {max_error:.2f} m")
        print(f"误差标准差: {std_error:.2f} m")
        print(f"90%误差: {percentile_90:.2f} m")
        print(f"95%误差: {percentile_95:.2f} m")

        print("\n--- 各维度平均误差 ---")
        print(f"X轴平均误差: {avg_error_x:.2f} m")
        print(f"Y轴平均误差: {avg_error_y:.2f} m")
        print(f"Z轴平均误差: {avg_error_z:.2f} m")

        print("\n--- 误差分布 ---")
        print(f"误差<50m的样本比例: {within_50m:.2f}%")
        print(f"误差<100m的样本比例: {within_100m:.2f}%")
        print(f"误差<200m的样本比例: {within_200m:.2f}%")

        # 绘制处理时间和误差分析图
        plt.figure(figsize=(15, 10))

        # 处理时间直方图
        plt.subplot(2, 2, 1)
        plt.hist(np.array(processing_times)*1000, bins=20, color='skyblue', edgecolor='black', alpha=0.7)
        plt.axvline(x=avg_time*1000, color='red', linestyle='--', label=f'平均: {avg_time*1000:.2f} ms')
        plt.title('实时定位处理时间分布', fontsize=14)
        plt.xlabel('处理时间 (ms)', fontsize=12)
        plt.ylabel('数量', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()

        # 误差直方图
        plt.subplot(2, 2, 2)
        plt.hist(errors, bins=20, color='lightgreen', edgecolor='black', alpha=0.7)
        plt.axvline(x=avg_error, color='red', linestyle='--', label=f'平均: {avg_error:.2f} m')
        plt.axvline(x=percentile_90, color='orange', linestyle='--', label=f'90%: {percentile_90:.2f} m')
        plt.title('定位误差分布', fontsize=14)
        plt.xlabel('误差 (m)', fontsize=12)
        plt.ylabel('数量', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()

        # 各维度误差对比
        plt.subplot(2, 2, 3)
        dimensions = ['X轴', 'Y轴', 'Z轴', '3D总误差']
        avg_errors = [avg_error_x, avg_error_y, avg_error_z, avg_error]
        colors = ['#1E88E5', '#FFC107', '#4CAF50', '#F44336']
        plt.bar(dimensions, avg_errors, color=colors, alpha=0.7)
        plt.title('各维度平均误差对比', fontsize=14)
        plt.xlabel('维度', fontsize=12)
        plt.ylabel('误差 (m)', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)

        # 误差累积分布
        plt.subplot(2, 2, 4)
        sorted_errors = np.sort(errors)
        cumulative = np.arange(1, len(sorted_errors) + 1) / len(sorted_errors)
        plt.plot(sorted_errors, cumulative, 'b-', linewidth=2)
        plt.axhline(y=0.9, color='orange', linestyle='--', label=f'90%: {percentile_90:.2f} m')
        plt.axvline(x=100, color='green', linestyle='--', label=f'100m: {within_100m:.1f}%')
        plt.title('误差累积分布函数 (CDF)', fontsize=14)
        plt.xlabel('误差 (m)', fontsize=12)
        plt.ylabel('累积概率', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()

        plt.tight_layout()
        plt.savefig('realtime_performance.png', dpi=300, bbox_inches='tight')

        # 返回性能数据
        performance_data = {
            'avg_time': avg_time,
            'processing_times': processing_times,
            'avg_error': avg_error,
            'median_error': median_error,
            'max_error': max_error,
            'min_error': min_error,
            'percentile_90': percentile_90,
            'percentile_95': percentile_95,
            'within_100m': within_100m
        }

        return avg_time, performance_data
    except Exception as e:
        print(f"性能测试失败: {str(e)}")
        return None, None

if __name__ == "__main__":
    # 设置参数
    g = 2
    T = 10
    del_T = 0.1
    snr_db = 12  # 信噪比dB (≥12dB)
    alt_kft = 10
    vel = 200

    # 真实位置和初始估计
    p_true = np.array([500, 0, 0, 1e9])  # 目标位置
    p_est_init = np.array([450, 50, 50, 1.01e9])  # 初始估计
    fo = 1e9
    L = 1

    if USE_TENSORFLOW:
        # 添加命令行参数解析
        parser = argparse.ArgumentParser(description='多普勒-AoA定位系统')
        parser.add_argument('--train', action='store_true', help='强制重新训练模型')
        parser.add_argument('--realtime', action='store_true', help='测试实时定位性能')
        parser.add_argument('--tests', type=int, default=100, help='实时测试次数')
        parser.add_argument('--clean', action='store_true', help='删除已有模型文件并重新训练')
        parser.add_argument('--debug', action='store_true', help='显示详细调试信息')
        args = parser.parse_args()

        # 设置调试模式
        if args.debug:
            print("调试模式已启用，将显示详细信息")
            tf.get_logger().setLevel('INFO')
        else:
            # 减少TensorFlow的日志输出
            tf.get_logger().setLevel('ERROR')

        # 如果指定了--clean参数，删除已有模型文件
        if args.clean:
            import os
            print("清理模式：将删除所有已有模型文件")
            model_files = [
                'best_location_model.keras',  # 新格式
                'best_location_model.h5',     # 旧格式
                'best_location_model.weights.h5',
                'normalization_params.npy',
                'model_structure.json'        # 模型结构JSON
            ]
            for file in model_files:
                if os.path.exists(file):
                    try:
                        os.remove(file)
                        print(f"已删除文件: {file}")
                    except Exception as e:
                        print(f"删除文件 {file} 失败: {str(e)}")

            # 删除TensorBoard日志
            if os.path.exists('./logs'):
                try:
                    import shutil
                    shutil.rmtree('./logs')
                    print("已删除TensorBoard日志目录")
                except Exception as e:
                    print(f"删除TensorBoard日志目录失败: {str(e)}")

            # 强制重新训练
            args.train = True
            print("已设置为训练模式")

        if args.realtime:
            # 测试实时定位性能
            print("开始实时定位性能测试...")
            avg_time, performance_data = test_realtime_performance(num_tests=args.tests)
            if avg_time:
                print(f"\n实时定位模式已验证，平均处理时间: {avg_time*1000:.2f} ms")
                print(f"处理频率: {1/avg_time:.2f} Hz (每秒可处理定位请求数)")

                # 显示简要误差信息
                if performance_data:
                    print(f"平均定位误差: {performance_data['avg_error']:.2f} m")
                    print(f"中位数误差: {performance_data['median_error']:.2f} m")
                    print(f"90%误差: {performance_data['percentile_90']:.2f} m")
                    print(f"误差<100m的样本比例: {performance_data['within_100m']:.2f}%")

                print("\n详细性能报告已保存到 realtime_performance.png")
            exit(0)

        # 默认尝试加载已有模型，除非指定--train参数
        print("\n" + "="*50)
        print("运行神经网络辅助定位...")
        print(f"训练模式: {'是' if args.train else '否'} (使用--train参数可强制重新训练)")
        print("="*50 + "\n")

        # 运行主函数
        run_doppler_aoa_with_nn(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L,
                        train_model=args.train)

        # 比较两种方法
        print("\n比较传统方法和神经网络方法...")
        compare_methods(g, T, del_T, snr_db, alt_kft, vel, p_true, p_est_init, fo, L)
    else:
        # 如果没有TensorFlow，只运行传统方法
        print("只运行传统方法（未安装TensorFlow）")

        # 生成轨迹数据
        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
        t = np.arange(0, len(Px)) * del_T  # 用于绘图，不影响计算
        Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

        # 生成带噪声的测量值 - 使用复杂噪声模型
        f_obs, phi_obs, freq_accuracy, phi_accuracy = generate_noisy_data(
            p_true, Plat_Nav_Data, mu_vect, fo, snr_db, complex_noise=True)

        # 运行传统优化
        try:
            result = least_squares(
                lambda params: residuals(params, Plat_Nav_Data, mu_vect, fo, L, f_obs, phi_obs, freq_accuracy, phi_accuracy),
                p_est_init,
                method='trf',
                ftol=1e-10,
                xtol=1e-10,
                gtol=1e-10,
                max_nfev=5000,
                verbose=1
            )
            p_est = result.x

            # 计算误差
            error_vector = p_est[:3] - p_true[:3]
            error_distance = np.linalg.norm(error_vector)

            print("\n传统方法最终估计位置:", p_est[:3])
            print("估计误差:", error_distance, "米")

            # 计算CRLB
            CRLB = calculate_crlb(p_est, Plat_Nav_Data, mu_vect, fo, L, freq_accuracy, phi_accuracy)
            C_xy = CRLB[:2, :2]

            # 绘制结果
            plt.figure(figsize=(12, 10))
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.plot(Px, Py, '-', color='#1E88E5', linewidth=2, label='平台路径')
            plt.scatter(p_est[0], p_est[1], s=120, marker='o', color='#1E88E5',
                    edgecolor='white', linewidth=2, label='估计位置', zorder=3)
            plt.scatter(p_true[0], p_true[1], s=150, marker='x', color='#D81B60',
                    linewidth=2.5, label='真实位置', zorder=3)

            # 尝试绘制误差椭圆
            try:
                ellipse = draw_error_ellipse(plt.gca(), p_true[:2], C_xy, k=2,
                                edgecolor='#D81B60', facecolor='none',
                                linewidth=2, alpha=0.8, zorder=2)
                # 为图例添加代理
                from matplotlib.lines import Line2D
                ellipse_proxy = Line2D([0], [0], color='#D81B60', lw=2)
                plt.legend([
                    Line2D([0], [0], color='#1E88E5', lw=2),
                    Line2D([0], [0], marker='o', color='#1E88E5', markersize=10, markeredgecolor='white'),
                    Line2D([0], [0], marker='x', color='#D81B60', markersize=10, linestyle=''),
                    ellipse_proxy
                ], ['平台路径', '估计位置', '真实位置', '95%置信区间'],
                loc='upper right', frameon=True, framealpha=0.9, fontsize=12)
            except Exception as e:
                print(f"警告: 误差椭圆绘制失败: {str(e)}")
                plt.legend(fontsize=12)

            # 设置标题和标签
            plt.title(f'发射机位置估计 (方法: 传统最小二乘法, 误差: {error_distance:.2f}m)', fontsize=14)
            plt.xlabel('X位置 (米)', fontsize=12)
            plt.ylabel('Y位置 (米)', fontsize=12)
            plt.axis('equal')
            plt.tight_layout()
            plt.savefig('location_estimation.png', dpi=300)

            # 打印详细结果
            print("\n多普勒-AoA估计结果:")
            print("-------------------------")
            print(f"X轴误差: {error_vector[0]:.2f} m")
            print(f"Y轴误差: {error_vector[1]:.2f} m")
            print(f"Z轴误差: {error_vector[2]:.2f} m")
            print(f"3D误差: {error_distance:.2f} m")

        except Exception as e:
            print(f"优化失败: {str(e)}")